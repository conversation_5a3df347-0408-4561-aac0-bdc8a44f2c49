# Agricultural AI System Requirements

# Core ML/Data Science Libraries
numpy==1.24.3
pandas==2.0.3
scikit-learn==1.3.0
scipy==1.11.1

# Visualization
matplotlib==3.7.2
seaborn==0.12.2
plotly==5.15.0

# Machine Learning Models
xgboost==1.7.6
lightgbm==4.0.0
catboost==1.2

# Deep Learning (for disease detection)
tensorflow==2.13.0
keras==2.13.1
torch==2.0.1
torchvision==0.15.2

# Image Processing
Pillow==10.0.0
opencv-python==********
imageio==2.31.1

# Web Framework
fastapi==0.103.0
uvicorn==0.23.2
jinja2==3.1.2
python-multipart==0.0.6
aiofiles==23.2.1

# Data Processing
joblib==1.3.2
openpyxl==3.1.2

# Utilities
tqdm==4.65.0
requests==2.31.0

# Development
jupyter==1.0.0
notebook==7.0.2
ipython==8.14.0

# Additional ML utilities
imbalanced-learn==0.11.0
feature-engine==1.6.2
yellowbrick==1.5

# Statistical analysis
statsmodels==0.14.0
