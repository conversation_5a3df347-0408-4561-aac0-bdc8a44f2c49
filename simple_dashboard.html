<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌾 Agricultural AI System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            padding: 40px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        
        input[type="number"], input[type="file"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        input[type="number"]:focus, input[type="file"]:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
        }
        
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            font-weight: 600;
            text-align: center;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }
        
        .result.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .image-preview {
            max-width: 100%;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 10px;
            display: none;
        }
        
        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #2ecc71;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌾 Agricultural AI System</h1>
            <p>Smart farming solutions powered by machine learning</p>
        </div>
        
        <div class="main-content">
            <!-- Crop Recommendation Section -->
            <div class="section">
                <h2>🌱 Crop Recommendation</h2>
                <form id="cropForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="nitrogen">Nitrogen (N)</label>
                            <input type="number" id="nitrogen" name="N" step="0.1" required>
                        </div>
                        <div class="form-group">
                            <label for="phosphorus">Phosphorus (P)</label>
                            <input type="number" id="phosphorus" name="P" step="0.1" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="potassium">Potassium (K)</label>
                            <input type="number" id="potassium" name="K" step="0.1" required>
                        </div>
                        <div class="form-group">
                            <label for="temperature">Temperature (°C)</label>
                            <input type="number" id="temperature" name="temperature" step="0.1" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="humidity">Humidity (%)</label>
                            <input type="number" id="humidity" name="humidity" step="0.1" required>
                        </div>
                        <div class="form-group">
                            <label for="ph">pH Level</label>
                            <input type="number" id="ph" name="ph" step="0.1" min="0" max="14" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="rainfall">Rainfall (mm)</label>
                        <input type="number" id="rainfall" name="rainfall" step="0.1" required>
                    </div>
                    
                    <button type="submit" class="btn" id="cropBtn">
                        Get Crop Recommendation
                    </button>
                    
                    <div id="cropResult" class="result"></div>
                </form>
            </div>
            
            <!-- Plant Disease Detection Section -->
            <div class="section">
                <h2>🔬 Plant Disease Detection</h2>
                <form id="diseaseForm">
                    <div class="form-group">
                        <label for="plantImage">Upload Plant Image</label>
                        <input type="file" id="plantImage" name="image" accept="image/*" required>
                        <img id="imagePreview" class="image-preview" alt="Preview">
                    </div>
                    
                    <button type="submit" class="btn" id="diseaseBtn">
                        Detect Disease
                    </button>
                    
                    <div id="diseaseResult" class="result"></div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="status" id="systemStatus">
        <div class="loading"></div>
        Checking system status...
    </div>

    <script>
        // Check system status
        function checkStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('systemStatus');
                    if (data.status === 'ready') {
                        statusDiv.innerHTML = '✅ System Ready';
                        statusDiv.style.borderLeftColor = '#2ecc71';
                    } else {
                        statusDiv.innerHTML = '⚠️ Loading models...';
                        statusDiv.style.borderLeftColor = '#f39c12';
                    }
                })
                .catch(error => {
                    document.getElementById('systemStatus').innerHTML = '❌ System Error';
                    document.getElementById('systemStatus').style.borderLeftColor = '#e74c3c';
                });
        }
        
        // Image preview
        document.getElementById('plantImage').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('imagePreview');
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Crop recommendation form
        document.getElementById('cropForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const btn = document.getElementById('cropBtn');
            const result = document.getElementById('cropResult');
            
            btn.disabled = true;
            btn.innerHTML = '<div class="loading"></div>Analyzing...';
            result.classList.remove('show');
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            fetch('/api/crop-recommendation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                result.textContent = data.message;
                result.className = `result ${data.success ? 'success' : 'error'}`;
                result.classList.add('show');
            })
            .catch(error => {
                result.textContent = 'Error: ' + error.message;
                result.className = 'result error';
                result.classList.add('show');
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = 'Get Crop Recommendation';
            });
        });
        
        // Disease detection form
        document.getElementById('diseaseForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const btn = document.getElementById('diseaseBtn');
            const result = document.getElementById('diseaseResult');
            
            btn.disabled = true;
            btn.innerHTML = '<div class="loading"></div>Analyzing...';
            result.classList.remove('show');
            
            const formData = new FormData(this);
            
            fetch('/api/disease-detection', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                result.textContent = data.message;
                result.className = `result ${data.success ? 'success' : 'error'}`;
                result.classList.add('show');
            })
            .catch(error => {
                result.textContent = 'Error: ' + error.message;
                result.className = 'result error';
                result.classList.add('show');
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = 'Detect Disease';
            });
        });
        
        // Initialize
        checkStatus();
        
        // Check status every 30 seconds
        setInterval(checkStatus, 30000);
    </script>
</body>
</html>