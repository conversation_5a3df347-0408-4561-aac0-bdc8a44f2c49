#!/usr/bin/env python3
"""
🌾 Agricultural AI Web Application
A comprehensive web application for crop recommendation, yield prediction, 
fertilizer recommendation, and plant disease detection with Gemini AI integration.
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

import numpy as np
import pandas as pd
from flask import Flask, render_template, request, jsonify, flash, redirect, url_for
from werkzeug.utils import secure_filename
import joblib

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'agricultural_ai_secret_key_2024'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Configuration
UPLOAD_FOLDER = 'uploads'
MODELS_FOLDER = 'models'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

# Ensure directories exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(MODELS_FOLDER, exist_ok=True)

# Gemini AI Integration
GEMINI_API_KEY = "AIzaSyAPuwa4UB95dbibgSfrESXDrj5Sy2JQkJM"

try:
    import google.generativeai as genai
    genai.configure(api_key=GEMINI_API_KEY)
    gemini_model = genai.GenerativeModel('gemini-1.5-flash')
    GEMINI_AVAILABLE = True
    logger.info("✅ Gemini AI configured successfully")
except ImportError:
    logger.warning("⚠️ Google Generative AI not available. Installing...")
    import subprocess
    subprocess.run([sys.executable, "-m", "pip", "install", "google-generativeai"], check=True)
    import google.generativeai as genai
    genai.configure(api_key=GEMINI_API_KEY)
    gemini_model = genai.GenerativeModel('gemini-1.5-flash')
    GEMINI_AVAILABLE = True
    logger.info("✅ Gemini AI installed and configured")
except Exception as e:
    logger.error(f"❌ Gemini AI setup failed: {e}")
    GEMINI_AVAILABLE = False

# Global variables for models
crop_model = None
yield_model = None
fertilizer_model = None
disease_model = None

# Load all models
def load_models():
    """Load all trained models"""
    global crop_model, yield_model, fertilizer_model, disease_model
    
    models_loaded = []
    
    # Load crop recommendation model
    try:
        crop_model_path = os.path.join(MODELS_FOLDER, 'crop_recommendation_model.pkl')
        if os.path.exists(crop_model_path):
            crop_model = joblib.load(crop_model_path)
            models_loaded.append("Crop Recommendation")
            logger.info("✅ Crop recommendation model loaded")
    except Exception as e:
        logger.error(f"❌ Failed to load crop model: {e}")
    
    # Load yield prediction model
    try:
        yield_model_path = os.path.join(MODELS_FOLDER, 'crop_yield_prediction_model.pkl')
        if os.path.exists(yield_model_path):
            yield_model = joblib.load(yield_model_path)
            models_loaded.append("Yield Prediction")
            logger.info("✅ Yield prediction model loaded")
    except Exception as e:
        logger.error(f"❌ Failed to load yield model: {e}")
    
    # Load fertilizer recommendation model
    try:
        fertilizer_model_path = os.path.join(MODELS_FOLDER, 'fertilizer_recommendation_model.pkl')
        if os.path.exists(fertilizer_model_path):
            fertilizer_model = joblib.load(fertilizer_model_path)
            models_loaded.append("Fertilizer Recommendation")
            logger.info("✅ Fertilizer recommendation model loaded")
    except Exception as e:
        logger.error(f"❌ Failed to load fertilizer model: {e}")
    
    # Load plant disease detection model
    try:
        # Check for the disease model in the plant_disease_hybrid folder
        disease_model_dirs = [d for d in os.listdir(MODELS_FOLDER) if d.startswith('plant_disease_hybrid_')]
        if disease_model_dirs:
            disease_model_path = os.path.join(MODELS_FOLDER, disease_model_dirs[0], 'plant_disease_hybrid_model.keras')
            if os.path.exists(disease_model_path):
                try:
                    import tensorflow as tf
                    disease_model = tf.keras.models.load_model(disease_model_path)
                    models_loaded.append("Plant Disease Detection")
                    logger.info("✅ Plant disease detection model loaded")
                except ImportError:
                    logger.warning("⚠️ TensorFlow not available for disease model")
    except Exception as e:
        logger.error(f"❌ Failed to load disease model: {e}")
    
    logger.info(f"📊 Models loaded: {', '.join(models_loaded) if models_loaded else 'None'}")
    return models_loaded

# Helper functions
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_gemini_recommendation(prediction_result, prediction_type):
    """Get AI-powered recommendations from Gemini"""
    if not GEMINI_AVAILABLE:
        return "AI recommendations not available."
    
    try:
        prompt = f"""
        As an agricultural AI expert, provide detailed recommendations based on this {prediction_type} result:
        
        Prediction: {prediction_result}
        
        Please provide:
        1. Detailed explanation of the result
        2. Actionable recommendations for farmers
        3. Best practices to follow
        4. Potential challenges and solutions
        5. Expected outcomes
        
        Keep it practical, concise, and farmer-friendly.
        """
        
        response = gemini_model.generate_content(prompt)
        return response.text
    except Exception as e:
        logger.error(f"Gemini API error: {e}")
        return f"AI recommendation service temporarily unavailable. Basic result: {prediction_result}"

# Prediction functions
def predict_crop_recommendation(data):
    """Predict crop recommendation"""
    if not crop_model:
        return {"error": "Crop recommendation model not available"}
    
    try:
        # Prepare input data
        input_df = pd.DataFrame([{
            'N': data['nitrogen'],
            'P': data['phosphorus'], 
            'K': data['potassium'],
            'temperature': data['temperature'],
            'humidity': data['humidity'],
            'ph': data['ph'],
            'rainfall': data['rainfall']
        }])
        
        # Apply feature engineering (same as training)
        input_df['N_P_ratio'] = input_df['N'] / (input_df['P'] + 1e-6)
        input_df['N_K_ratio'] = input_df['N'] / (input_df['K'] + 1e-6)
        input_df['P_K_ratio'] = input_df['P'] / (input_df['K'] + 1e-6)
        input_df['NPK_sum'] = input_df['N'] + input_df['P'] + input_df['K']
        input_df['NPK_product'] = input_df['N'] * input_df['P'] * input_df['K']
        input_df['temp_humidity_ratio'] = input_df['temperature'] / (input_df['humidity'] + 1e-6)
        input_df['temp_rainfall_ratio'] = input_df['temperature'] / (input_df['rainfall'] + 1e-6)
        input_df['ph_acidic'] = (input_df['ph'] < 6.5).astype(int)
        input_df['ph_neutral'] = ((input_df['ph'] >= 6.5) & (input_df['ph'] <= 7.5)).astype(int)
        input_df['ph_alkaline'] = (input_df['ph'] > 7.5).astype(int)
        input_df['rainfall_low'] = (input_df['rainfall'] < 100).astype(int)
        input_df['rainfall_medium'] = ((input_df['rainfall'] >= 100) & (input_df['rainfall'] <= 200)).astype(int)
        input_df['rainfall_high'] = (input_df['rainfall'] > 200).astype(int)
        input_df['temp_cool'] = (input_df['temperature'] < 20).astype(int)
        input_df['temp_moderate'] = ((input_df['temperature'] >= 20) & (input_df['temperature'] <= 30)).astype(int)
        input_df['temp_hot'] = (input_df['temperature'] > 30).astype(int)
        
        # Ensure same column order as training
        feature_names = crop_model['feature_names']
        input_df = input_df[feature_names]
        
        # Scale features
        input_scaled = crop_model['scaler'].transform(input_df)
        
        # Make prediction
        if crop_model.get('is_stacked', False):
            # Handle stacked model prediction
            model = crop_model['model']
            prediction = model.predict(input_scaled)[0]
        else:
            model = crop_model['model']
            prediction = model.predict(input_scaled)[0]
        
        # Get confidence
        if hasattr(model, 'predict_proba'):
            probabilities = model.predict_proba(input_scaled)[0]
            confidence = probabilities.max()
        else:
            confidence = 0.95  # Default confidence
        
        # Decode prediction
        predicted_crop = crop_model['label_encoder'].inverse_transform([prediction])[0]
        
        return {
            "predicted_crop": predicted_crop,
            "confidence": float(confidence),
            "accuracy": crop_model['model_accuracy']
        }
        
    except Exception as e:
        logger.error(f"Crop prediction error: {e}")
        return {"error": str(e)}

def predict_fertilizer_recommendation(data):
    """Predict fertilizer recommendation"""
    if not fertilizer_model:
        return {"error": "Fertilizer recommendation model not available"}
    
    try:
        # This is a placeholder - implement based on your fertilizer model structure
        predicted_fertilizer = "NPK Fertilizer"  # Default
        confidence = 0.85
        
        return {
            "predicted_fertilizer": predicted_fertilizer,
            "confidence": confidence,
            "district": data.get('district', 'Unknown'),
            "crop": data.get('crop', 'Unknown')
        }
        
    except Exception as e:
        logger.error(f"Fertilizer prediction error: {e}")
        return {"error": str(e)}

def predict_yield(data):
    """Predict crop yield"""
    if not yield_model:
        return {"error": "Yield prediction model not available"}
    
    try:
        # This is a placeholder - implement based on your yield model structure
        predicted_yield = 2.5  # Default yield in tons/hectare
        
        return {
            "predicted_yield": predicted_yield,
            "unit": "tons/hectare",
            "crop": data.get('crop', 'Unknown'),
            "state": data.get('state', 'Unknown')
        }
        
    except Exception as e:
        logger.error(f"Yield prediction error: {e}")
        return {"error": str(e)}

# Routes
@app.route('/')
def index():
    """Main dashboard"""
    return render_template('index.html')

@app.route('/crop_recommendation')
def crop_recommendation():
    """Crop recommendation page"""
    # Sample data for dropdowns
    states = ['Andhra Pradesh', 'Assam', 'Bihar', 'Gujarat', 'Karnataka', 'Kerala', 
              'Madhya Pradesh', 'Maharashtra', 'Punjab', 'Rajasthan', 'Tamil Nadu', 'Uttar Pradesh']
    
    soil_types = ['Alluvial', 'Black', 'Red', 'Laterite', 'Desert', 'Mountain']
    
    return render_template('crop_recommendation.html', states=states, soil_types=soil_types)

@app.route('/api/crop_recommendation', methods=['POST'])
def api_crop_recommendation():
    """API endpoint for crop recommendation"""
    try:
        data = request.json
        
        # Validate required fields
        required_fields = ['nitrogen', 'phosphorus', 'potassium', 'temperature', 'humidity', 'ph', 'rainfall']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing field: {field}"}), 400
            
        # Make prediction
        result = predict_crop_recommendation(data)
        
        if "error" in result:
            return jsonify(result), 400
        
        # Get AI recommendations
        ai_recommendation = get_gemini_recommendation(
            f"Recommended crop: {result['predicted_crop']} with {result['confidence']*100:.1f}% confidence",
            "crop recommendation"
        )
        
        result['ai_recommendation'] = ai_recommendation
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Crop recommendation API error: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/fertilizer_recommendation')
def fertilizer_recommendation():
    """Fertilizer recommendation page"""
    # Sample data for dropdowns
    districts = ['Kolhapur', 'Pune', 'Mumbai', 'Nashik', 'Aurangabad', 'Nagpur', 'Solapur']
    crops = ['Rice', 'Wheat', 'Cotton', 'Sugarcane', 'Maize', 'Soybean', 'Groundnut']
    soil_colors = ['Black', 'Red', 'Brown', 'Yellow', 'Grey']
    
    return render_template('fertilizer_recommendation.html', 
                         districts=districts, crops=crops, soil_colors=soil_colors)

@app.route('/api/fertilizer_recommendation', methods=['POST'])
def api_fertilizer_recommendation():
    """API endpoint for fertilizer recommendation"""
    try:
        data = request.json
        result = predict_fertilizer_recommendation(data)
        
        if "error" in result:
            return jsonify(result), 400
        
        # Get AI recommendations
        ai_recommendation = get_gemini_recommendation(
            f"Recommended fertilizer: {result['predicted_fertilizer']} for {result['crop']} in {result['district']}",
            "fertilizer recommendation"
        )
        
        result['ai_recommendation'] = ai_recommendation
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Fertilizer recommendation API error: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/yield_prediction')
def yield_prediction():
    """Yield prediction page"""
    # Sample data for dropdowns
    crops = ['Rice', 'Wheat', 'Cotton', 'Sugarcane', 'Maize', 'Soybean', 'Groundnut', 'Jowar', 'Bajra']
    states = ['Andhra Pradesh', 'Assam', 'Bihar', 'Gujarat', 'Karnataka', 'Kerala', 
              'Madhya Pradesh', 'Maharashtra', 'Punjab', 'Rajasthan', 'Tamil Nadu', 'Uttar Pradesh']
    seasons = ['Kharif', 'Rabi', 'Whole Year', 'Summer']
    
    return render_template('yield_prediction.html', crops=crops, states=states, seasons=seasons)

@app.route('/api/yield_prediction', methods=['POST'])
def api_yield_prediction():
    """API endpoint for yield prediction"""
    try:
        data = request.json
        result = predict_yield(data)
        
        if "error" in result:
            return jsonify(result), 400
        
        # Get AI recommendations
        ai_recommendation = get_gemini_recommendation(
            f"Predicted yield: {result['predicted_yield']} {result['unit']} for {result['crop']} in {result['state']}",
            "yield prediction"
        )
        
        result['ai_recommendation'] = ai_recommendation
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Yield prediction API error: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/disease_detection')
def disease_detection():
    """Plant disease detection page"""
    return render_template('disease_detection.html')

@app.route('/api/disease_detection', methods=['POST'])
def api_disease_detection():
    """API endpoint for plant disease detection"""
    try:
        if 'file' not in request.files:
            return jsonify({"error": "No file uploaded"}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(UPLOAD_FOLDER, filename)
            file.save(filepath)
            
            # Placeholder disease detection result
            result = {
                "predicted_disease": "Healthy",
                "confidence": 0.92,
                "filename": filename
            }
            
            # Get AI recommendations
            ai_recommendation = get_gemini_recommendation(
                f"Plant condition: {result['predicted_disease']} with {result['confidence']*100:.1f}% confidence",
                "plant disease detection"
            )
            
            result['ai_recommendation'] = ai_recommendation
            
            return jsonify(result)
        
        return jsonify({"error": "Invalid file type"}), 400
        
    except Exception as e:
        logger.error(f"Disease detection API error: {e}")
        return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    # Load models on startup
    load_models()
    
    # Run the app
    app.run(debug=True, host='0.0.0.0', port=5000)