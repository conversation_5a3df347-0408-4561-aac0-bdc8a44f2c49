<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agricultural AI System Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #28a745;
            --secondary-color: #6c757d;
            --success-color: #20c997;
            --info-color: #17a2b8;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }
        
        body {
            background: linear-gradient(135deg, #e8f5e8 0%, #f8f9fa 100%);
            font-family: 'Arial', sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, #20c997 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .card-header {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-bottom: 2px solid var(--primary-color);
            border-radius: 15px 15px 0 0 !important;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, #20c997 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .result-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-left: 4px solid var(--primary-color);
        }
        
        .icon-box {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 15px;
        }
        
        .icon-crop { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .icon-yield { background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%); }
        .icon-fertilizer { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); }
        .icon-disease { background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%); }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }
        
        @media (max-width: 768px) {
            .card {
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-seedling me-2"></i>
                Agricultural AI System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#crop-section">Crop Recommendation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#yield-section">Yield Prediction</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#fertilizer-section">Fertilizer Recommendation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#disease-section">Disease Detection</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1 class="display-4 fw-bold text-success">
                        <i class="fas fa-leaf me-3"></i>
                        Smart Agriculture AI Dashboard
                    </h1>
                    <p class="lead text-muted">
                        Optimize your farming decisions with AI-powered recommendations for crops, yield, fertilizers, and disease detection
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Crop Recommendation Section -->
        <section id="crop-section" class="mb-5">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex align-items-center">
                                <div class="icon-box icon-crop me-3">
                                    <i class="fas fa-seedling"></i>
                                </div>
                                <div>
                                    <h4 class="mb-0">Crop Recommendation System</h4>
                                    <small class="text-muted">Get the best crop recommendation based on soil and climate conditions</small>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <form id="cropForm">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Nitrogen (N)</label>
                                            <input type="number" class="form-control" id="cropN" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Phosphorus (P)</label>
                                            <input type="number" class="form-control" id="cropP" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Potassium (K)</label>
                                            <input type="number" class="form-control" id="cropK" step="0.01" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">Temperature (°C)</label>
                                            <input type="number" class="form-control" id="cropTemp" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">Humidity (%)</label>
                                            <input type="number" class="form-control" id="cropHumidity" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">pH</label>
                                            <input type="number" class="form-control" id="cropPh" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">Rainfall (mm)</label>
                                            <input type="number" class="form-control" id="cropRainfall" step="0.01" required>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Get Crop Recommendation
                                </button>
                                <div class="loading ms-3">
                                    <div class="spinner-border spinner-border-sm" role="status"></div>
                                    <span class="ms-2">Analyzing...</span>
                                </div>
                            </form>
                            <div id="cropResult" class="mt-4"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Yield Prediction Section -->
        <section id="yield-section" class="mb-5">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex align-items-center">
                                <div class="icon-box icon-yield me-3">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div>
                                    <h4 class="mb-0">Crop Yield Prediction</h4>
                                    <small class="text-muted">Predict expected crop yield based on various factors</small>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <form id="yieldForm">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Crop</label>
                                            <select class="form-control" id="yieldCrop" required>
                                                <option value="">Select Crop</option>
                                                <option value="Rice">Rice</option>
                                                <option value="Wheat">Wheat</option>
                                                <option value="Maize">Maize</option>
                                                <option value="Sugarcane">Sugarcane</option>
                                                <option value="Cotton">Cotton</option>
                                                <option value="Arhar/Tur">Arhar/Tur</option>
                                                <option value="Gram">Gram</option>
                                                <option value="Groundnut">Groundnut</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">State</label>
                                            <select class="form-control" id="yieldState" required>
                                                <option value="">Select State</option>
                                                <option value="West Bengal">West Bengal</option>
                                                <option value="Uttar Pradesh">Uttar Pradesh</option>
                                                <option value="Punjab">Punjab</option>
                                                <option value="Haryana">Haryana</option>
                                                <option value="Bihar">Bihar</option>
                                                <option value="Tamil Nadu">Tamil Nadu</option>
                                                <option value="Karnataka">Karnataka</option>
                                                <option value="Maharashtra">Maharashtra</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Season</label>
                                            <select class="form-control" id="yieldSeason" required>
                                                <option value="">Select Season</option>
                                                <option value="Kharif">Kharif</option>
                                                <option value="Rabi">Rabi</option>
                                                <option value="Whole Year">Whole Year</option>
                                                <option value="Autumn">Autumn</option>
                                                <option value="Summer">Summer</option>
                                                <option value="Winter">Winter</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">Year</label>
                                            <input type="number" class="form-control" id="yieldYear" min="2020" max="2030" value="2024" required>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">Area (hectares)</label>
                                            <input type="number" class="form-control" id="yieldArea" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">Production (tonnes)</label>
                                            <input type="number" class="form-control" id="yieldProduction" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">Rainfall (mm)</label>
                                            <input type="number" class="form-control" id="yieldRainfall" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">Fertilizer (kg)</label>
                                            <input type="number" class="form-control" id="yieldFertilizer" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">Pesticide (kg)</label>
                                            <input type="number" class="form-control" id="yieldPesticide" step="0.01" required>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-chart-bar me-2"></i>Predict Yield
                                </button>
                                <div class="loading ms-3">
                                    <div class="spinner-border spinner-border-sm" role="status"></div>
                                    <span class="ms-2">Calculating...</span>
                                </div>
                            </form>
                            <div id="yieldResult" class="mt-4"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Fertilizer Recommendation Section -->
        <section id="fertilizer-section" class="mb-5">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex align-items-center">
                                <div class="icon-box icon-fertilizer me-3">
                                    <i class="fas fa-flask"></i>
                                </div>
                                <div>
                                    <h4 class="mb-0">Fertilizer Recommendation</h4>
                                    <small class="text-muted">Get optimal fertilizer recommendations based on crop and soil analysis</small>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <form id="fertilizerForm">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Crop</label>
                                            <select class="form-control" id="fertCrop" required>
                                                <option value="">Select Crop</option>
                                                <option value="Rice">Rice</option>
                                                <option value="Wheat">Wheat</option>
                                                <option value="Maize">Maize</option>
                                                <option value="Sugarcane">Sugarcane</option>
                                                <option value="Cotton">Cotton</option>
                                                <option value="Groundnut">Groundnut</option>
                                                <option value="Jowar">Jowar</option>
                                                <option value="Tur">Tur</option>
                                                <option value="Gram">Gram</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Soil Color</label>
                                            <select class="form-control" id="fertSoilColor" required>
                                                <option value="">Select Soil Color</option>
                                                <option value="Black">Black</option>
                                                <option value="Red">Red</option>
                                                <option value="Dark Brown">Dark Brown</option>
                                                <option value="Reddish Brown">Reddish Brown</option>
                                                <option value="Light Brown">Light Brown</option>
                                                <option value="Medium Brown">Medium Brown</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">District</label>
                                            <select class="form-control" id="fertDistrict" required>
                                                <option value="">Select District</option>
                                                <option value="Kolhapur">Kolhapur</option>
                                                <option value="Solapur">Solapur</option>
                                                <option value="Sangli">Sangli</option>
                                                <option value="Pune">Pune</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">Nitrogen</label>
                                            <input type="number" class="form-control" id="fertN" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">Phosphorus</label>
                                            <input type="number" class="form-control" id="fertP" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">Potassium</label>
                                            <input type="number" class="form-control" id="fertK" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">pH</label>
                                            <input type="number" class="form-control" id="fertPh" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">Temperature (°C)</label>
                                            <input type="number" class="form-control" id="fertTemp" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">Rainfall (mm)</label>
                                            <input type="number" class="form-control" id="fertRainfall" step="0.01" required>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-leaf me-2"></i>Get Fertilizer Recommendation
                                </button>
                                <div class="loading ms-3">
                                    <div class="spinner-border spinner-border-sm" role="status"></div>
                                    <span class="ms-2">Analyzing...</span>
                                </div>
                            </form>
                            <div id="fertilizerResult" class="mt-4"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Disease Detection Section -->
        <section id="disease-section" class="mb-5">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex align-items-center">
                                <div class="icon-box icon-disease me-3">
                                    <i class="fas fa-microscope"></i>
                                </div>
                                <div>
                                    <h4 class="mb-0">Plant Disease Detection</h4>
                                    <small class="text-muted">Upload plant images to detect diseases using AI</small>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <form id="diseaseForm" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label class="form-label">Upload Plant Image</label>
                                    <input type="file" class="form-control" id="diseaseImage" accept="image/*" required>
                                    <div class="form-text">Supported formats: JPG, PNG, JPEG (Max size: 5MB)</div>
                                </div>
                                <div id="imagePreview" class="mb-3"></div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Analyze Image
                                </button>
                                <div class="loading ms-3">
                                    <div class="spinner-border spinner-border-sm" role="status"></div>
                                    <span class="ms-2">Analyzing image...</span>
                                </div>
                            </form>
                            <div id="diseaseResult" class="mt-4"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-leaf me-2"></i>Agricultural AI System</h5>
                    <p class="mb-0">Empowering farmers with AI-driven insights for better crop management and higher yields.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">© 2024 Agricultural AI System. All rights reserved.</p>
                    <small class="text-muted">Built with FastAPI, Bootstrap, and Machine Learning</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // API base URL
        const API_BASE = '';

        // Helper function to show loading
        function showLoading(form) {
            const loading = form.querySelector('.loading');
            const button = form.querySelector('button[type="submit"]');
            loading.classList.add('show');
            button.disabled = true;
        }

        // Helper function to hide loading
        function hideLoading(form) {
            const loading = form.querySelector('.loading');
            const button = form.querySelector('button[type="submit"]');
            loading.classList.remove('show');
            button.disabled = false;
        }

        // Helper function to show results
        function showResult(elementId, data, type) {
            const element = document.getElementById(elementId);
            let html = '';

            if (type === 'crop') {
                html = `
                    <div class="alert alert-success result-card">
                        <h5><i class="fas fa-seedling me-2"></i>Crop Recommendation Result</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Recommended Crop:</strong> ${data.recommended_crop}<br>
                                <strong>Confidence:</strong> ${(data.confidence * 100).toFixed(1)}%
                            </div>
                            <div class="col-md-6">
                                <strong>Input Conditions:</strong><br>
                                NPK: ${data.input_parameters.N}, ${data.input_parameters.P}, ${data.input_parameters.K}<br>
                                Climate: ${data.input_parameters.temperature}°C, ${data.input_parameters.humidity}%, ${data.input_parameters.rainfall}mm
                            </div>
                        </div>
                        <hr>
                        <strong>Recommendations:</strong>
                        <ul class="mb-0">
                            ${data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                `;
            } else if (type === 'yield') {
                html = `
                    <div class="alert alert-info result-card">
                        <h5><i class="fas fa-chart-line me-2"></i>Yield Prediction Result</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Predicted Yield:</strong> ${data.predicted_yield.toFixed(2)} units/area<br>
                                <strong>Expected Production:</strong> ${data.expected_production.toFixed(2)} units
                            </div>
                            <div class="col-md-6">
                                <strong>Current Productivity:</strong> ${data.current_productivity.toFixed(2)} units/area<br>
                                <strong>Crop:</strong> ${data.input_parameters.Crop} (${data.input_parameters.Season})
                            </div>
                        </div>
                        <hr>
                        <strong>Insights:</strong>
                        <ul class="mb-0">
                            ${data.insights.map(insight => `<li>${insight}</li>`).join('')}
                        </ul>
                    </div>
                `;
            } else if (type === 'fertilizer') {
                html = `
                    <div class="alert alert-warning result-card">
                        <h5><i class="fas fa-flask me-2"></i>Fertilizer Recommendation Result</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Recommended Fertilizer:</strong> ${data.recommended_fertilizer}<br>
                                <strong>Confidence:</strong> ${(data.confidence * 100).toFixed(1)}%
                            </div>
                            <div class="col-md-6">
                                <strong>Crop:</strong> ${data.input_parameters.Crop}<br>
                                <strong>Soil:</strong> ${data.input_parameters.Soil_color}
                            </div>
                        </div>
                        <hr>
                        <strong>Recommendations:</strong>
                        <ul class="mb-0">
                            ${data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                `;
            } else if (type === 'disease') {
                html = `
                    <div class="alert alert-danger result-card">
                        <h5><i class="fas fa-microscope me-2"></i>Disease Detection Result</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Detected Condition:</strong> ${data.predicted_disease}<br>
                                <strong>Confidence:</strong> ${(data.confidence * 100).toFixed(1)}%
                            </div>
                        </div>
                        <hr>
                        <strong>Recommendations:</strong>
                        <ul class="mb-0">
                            ${data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }

            element.innerHTML = html;
        }

        // Helper function to show error
        function showError(elementId, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Error:</strong> ${message}
                </div>
            `;
        }

        // Crop Recommendation Form
        document.getElementById('cropForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const form = e.target;
            showLoading(form);

            const data = {
                N: parseFloat(document.getElementById('cropN').value),
                P: parseFloat(document.getElementById('cropP').value),
                K: parseFloat(document.getElementById('cropK').value),
                temperature: parseFloat(document.getElementById('cropTemp').value),
                humidity: parseFloat(document.getElementById('cropHumidity').value),
                ph: parseFloat(document.getElementById('cropPh').value),
                rainfall: parseFloat(document.getElementById('cropRainfall').value)
            };

            try {
                const response = await fetch('/predict/crop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult('cropResult', result, 'crop');
                } else {
                    const error = await response.json();
                    showError('cropResult', error.detail || 'Prediction failed');
                }
            } catch (error) {
                showError('cropResult', 'Network error: ' + error.message);
            }

            hideLoading(form);
        });

        // Yield Prediction Form
        document.getElementById('yieldForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const form = e.target;
            showLoading(form);

            const data = {
                crop: document.getElementById('yieldCrop').value,
                state: document.getElementById('yieldState').value,
                season: document.getElementById('yieldSeason').value,
                crop_year: parseInt(document.getElementById('yieldYear').value),
                area: parseFloat(document.getElementById('yieldArea').value),
                production: parseFloat(document.getElementById('yieldProduction').value),
                annual_rainfall: parseFloat(document.getElementById('yieldRainfall').value),
                fertilizer: parseFloat(document.getElementById('yieldFertilizer').value),
                pesticide: parseFloat(document.getElementById('yieldPesticide').value)
            };

            try {
                const response = await fetch('/predict/yield', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult('yieldResult', result, 'yield');
                } else {
                    const error = await response.json();
                    showError('yieldResult', error.detail || 'Prediction failed');
                }
            } catch (error) {
                showError('yieldResult', 'Network error: ' + error.message);
            }

            hideLoading(form);
        });

        // Fertilizer Recommendation Form
        document.getElementById('fertilizerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const form = e.target;
            showLoading(form);

            const data = {
                crop: document.getElementById('fertCrop').value,
                soil_color: document.getElementById('fertSoilColor').value,
                district_name: document.getElementById('fertDistrict').value,
                nitrogen: parseFloat(document.getElementById('fertN').value),
                phosphorus: parseFloat(document.getElementById('fertP').value),
                potassium: parseFloat(document.getElementById('fertK').value),
                ph: parseFloat(document.getElementById('fertPh').value),
                temperature: parseFloat(document.getElementById('fertTemp').value),
                rainfall: parseFloat(document.getElementById('fertRainfall').value)
            };

            try {
                const response = await fetch('/predict/fertilizer', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult('fertilizerResult', result, 'fertilizer');
                } else {
                    const error = await response.json();
                    showError('fertilizerResult', error.detail || 'Prediction failed');
                }
            } catch (error) {
                showError('fertilizerResult', 'Network error: ' + error.message);
            }

            hideLoading(form);
        });

        // Disease Detection Form
        document.getElementById('diseaseForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const form = e.target;
            showLoading(form);

            const fileInput = document.getElementById('diseaseImage');
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);

            try {
                const response = await fetch('/predict/disease', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult('diseaseResult', result, 'disease');
                } else {
                    const error = await response.json();
                    showError('diseaseResult', error.detail || 'Analysis failed');
                }
            } catch (error) {
                showError('diseaseResult', 'Network error: ' + error.message);
            }

            hideLoading(form);
        });

        // Image preview for disease detection
        document.getElementById('diseaseImage').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const preview = document.getElementById('imagePreview');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `
                        <div class="text-center">
                            <img src="${e.target.result}" class="img-thumbnail" style="max-width: 300px; max-height: 300px;">
                            <p class="mt-2 text-muted">Selected: ${file.name}</p>
                        </div>
                    `;
                };
                reader.readAsDataURL(file);
            } else {
                preview.innerHTML = '';
            }
        });

        // Sample data buttons
        function fillSampleData(type) {
            if (type === 'crop') {
                document.getElementById('cropN').value = 90;
                document.getElementById('cropP').value = 42;
                document.getElementById('cropK').value = 43;
                document.getElementById('cropTemp').value = 20.87;
                document.getElementById('cropHumidity').value = 82.00;
                document.getElementById('cropPh').value = 6.50;
                document.getElementById('cropRainfall').value = 202.93;
            } else if (type === 'yield') {
                document.getElementById('yieldCrop').value = 'Rice';
                document.getElementById('yieldState').value = 'West Bengal';
                document.getElementById('yieldSeason').value = 'Kharif';
                document.getElementById('yieldYear').value = 2024;
                document.getElementById('yieldArea').value = 1000;
                document.getElementById('yieldProduction').value = 2500;
                document.getElementById('yieldRainfall').value = 1200;
                document.getElementById('yieldFertilizer').value = 150;
                document.getElementById('yieldPesticide').value = 25;
            } else if (type === 'fertilizer') {
                document.getElementById('fertCrop').value = 'Rice';
                document.getElementById('fertSoilColor').value = 'Black';
                document.getElementById('fertDistrict').value = 'Kolhapur';
                document.getElementById('fertN').value = 37;
                document.getElementById('fertP').value = 52;
                document.getElementById('fertK').value = 48;
                document.getElementById('fertPh').value = 6.5;
                document.getElementById('fertTemp').value = 28.5;
                document.getElementById('fertRainfall').value = 120.0;
            }
        }

        // Add sample data buttons to forms
        document.addEventListener('DOMContentLoaded', function() {
            // Add sample data buttons
            const cropForm = document.getElementById('cropForm');
            const yieldForm = document.getElementById('yieldForm');
            const fertilizerForm = document.getElementById('fertilizerForm');

            const sampleButtonHTML = `
                <button type="button" class="btn btn-outline-secondary ms-2" onclick="fillSampleData('{type}')">
                    <i class="fas fa-fill-drip me-1"></i>Fill Sample Data
                </button>
            `;

            cropForm.querySelector('button[type="submit"]').insertAdjacentHTML('afterend', 
                sampleButtonHTML.replace('{type}', 'crop'));
            yieldForm.querySelector('button[type="submit"]').insertAdjacentHTML('afterend', 
                sampleButtonHTML.replace('{type}', 'yield'));
            fertilizerForm.querySelector('button[type="submit"]').insertAdjacentHTML('afterend', 
                sampleButtonHTML.replace('{type}', 'fertilizer'));
        });
    </script>
</body>
</html>