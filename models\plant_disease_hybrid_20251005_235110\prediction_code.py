
import tensorflow as tf
import numpy as np
from PIL import Image

class PlantDiseaseDetector:
    def __init__(self, model_path):
        """Load the trained model"""
        self.model = tf.keras.models.load_model(model_path)
        self.class_names = ['Apple___Apple_scab', 'Apple___Black_rot', 'Apple___Cedar_apple_rust', 'Apple___healthy', 'Blueberry___healthy', 'Cherry_(including_sour)___Powdery_mildew', 'Cherry_(including_sour)___healthy', 'Corn_(maize)___Cercospora_leaf_spot Gray_leaf_spot', 'Corn_(maize)___Common_rust_', 'Corn_(maize)___Northern_Leaf_Blight', 'Corn_(maize)___healthy', 'Grape___Black_rot', 'Grape___Esca_(Black_Measles)', 'Grape___Leaf_blight_(Isariopsis_Leaf_Spot)', 'Grape___healthy', 'Orange___Haunglongbing_(Citrus_greening)', 'Peach___Bacterial_spot', 'Peach___healthy', 'Pepper,_bell___Bacterial_spot', 'Pepper,_bell___healthy', 'Potato___Early_blight', 'Potato___Late_blight', 'Potato___healthy', 'Raspberry___healthy', 'Soybean___healthy', 'Squash___Powdery_mildew', 'Strawberry___Leaf_scorch', 'Strawberry___healthy', 'Tomato___Bacterial_spot', 'Tomato___Early_blight', 'Tomato___Late_blight', 'Tomato___Leaf_Mold', 'Tomato___Septoria_leaf_spot', 'Tomato___Spider_mites Two-spotted_spider_mite', 'Tomato___Target_Spot', 'Tomato___Tomato_Yellow_Leaf_Curl_Virus', 'Tomato___Tomato_mosaic_virus', 'Tomato___healthy']
        self.img_shape = (128, 128, 3)

    def preprocess_image(self, image_path):
        """Preprocess image for prediction"""
        img = Image.open(image_path).convert('RGB')
        img = img.resize((self.img_shape[1], self.img_shape[0]))
        img_array = np.array(img) / 255.0
        img_array = np.expand_dims(img_array, axis=0)
        return img_array

    def predict(self, image_path):
        """Predict plant disease from image"""
        img_array = self.preprocess_image(image_path)
        predictions = self.model.predict(img_array)

        class_idx = np.argmax(predictions[0])
        confidence = predictions[0][class_idx]
        disease_name = self.class_names[class_idx]

        return {
            'disease': disease_name,
            'confidence': float(confidence),
            'all_predictions': dict(zip(self.class_names, predictions[0].tolist()))
        }

# Usage:
# detector = PlantDiseaseDetector('path/to/model.keras')
# result = detector.predict('path/to/plant_image.jpg')
# print(f"Disease: {result['disease']}, Confidence: {result['confidence']:.4f}")
