{% extends "base.html" %}

{% block title %}Fertilizer Recommendation - Agricultural AI{% endblock %}

{% block extra_css %}
<style>
    .fertilizer-form {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 2rem;
        color: white;
        margin-bottom: 2rem;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        border: none;
        padding: 12px 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.3);
        border: 2px solid #fff;
    }
    
    .result-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    
    .result-card:hover {
        transform: translateY(-5px);
    }
    
    .fertilizer-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .nutrient-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .confidence-bar {
        height: 10px;
        border-radius: 5px;
        background: #e9ecef;
        position: relative;
        overflow: hidden;
    }
    
    .confidence-fill {
        height: 100%;
        background: linear-gradient(90deg, #28a745, #20c997);
        border-radius: 5px;
        transition: width 0.8s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Header -->
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary">
            <i class="fas fa-flask me-3"></i>Fertilizer Recommendation System
        </h1>
        <p class="lead text-muted">
            Get optimized fertilizer recommendations based on soil conditions and crop requirements
        </p>
        <div class="d-flex justify-content-center gap-3 mb-4">
            <span class="badge bg-success fs-6">90% Accuracy</span>
            <span class="badge bg-info fs-6">Region-specific</span>
            <span class="badge bg-warning fs-6">Cost-effective</span>
        </div>
    </div>

    <div class="row">
        <!-- Input Form -->
        <div class="col-lg-6">
            <div class="fertilizer-form">
                <h3 class="mb-4">
                    <i class="fas fa-atom me-2"></i>Enter Crop & Soil Details
                </h3>
                
                <form id="fertilizerRecommendationForm">
                    <div class="row">
                        <!-- Crop Type -->
                        <div class="col-md-12 mb-3">
                            <label for="crop" class="form-label">
                                <i class="fas fa-seedling me-2"></i>Crop Type
                            </label>
                            <select class="form-select" id="crop" name="crop" required>
                                <option value="">Select Crop</option>
                                <option value="Barley">Barley</option>
                                <option value="Cotton">Cotton</option>
                                <option value="Ground Nuts">Ground Nuts</option>
                                <option value="Maize">Maize</option>
                                <option value="Millets">Millets</option>
                                <option value="Oil seeds">Oil seeds</option>
                                <option value="Paddy">Paddy (Rice)</option>
                                <option value="Pulses">Pulses</option>
                                <option value="Sugarcane">Sugarcane</option>
                                <option value="Tobacco">Tobacco</option>
                                <option value="Wheat">Wheat</option>
                            </select>
                        </div>

                        <!-- State -->
                        <div class="col-md-6 mb-3">
                            <label for="state" class="form-label">
                                <i class="fas fa-map-marker-alt me-2"></i>State
                            </label>
                            <select class="form-select" id="state" name="state" required>
                                <option value="">Select State</option>
                                <option value="Andhra Pradesh">Andhra Pradesh</option>
                                <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                                <option value="Assam">Assam</option>
                                <option value="Bihar">Bihar</option>
                                <option value="Chhattisgarh">Chhattisgarh</option>
                                <option value="Goa">Goa</option>
                                <option value="Gujarat">Gujarat</option>
                                <option value="Haryana">Haryana</option>
                                <option value="Himachal Pradesh">Himachal Pradesh</option>
                                <option value="Jharkhand">Jharkhand</option>
                                <option value="Karnataka">Karnataka</option>
                                <option value="Kerala">Kerala</option>
                                <option value="Madhya Pradesh">Madhya Pradesh</option>
                                <option value="Maharashtra">Maharashtra</option>
                                <option value="Manipur">Manipur</option>
                                <option value="Meghalaya">Meghalaya</option>
                                <option value="Mizoram">Mizoram</option>
                                <option value="Nagaland">Nagaland</option>
                                <option value="Odisha">Odisha</option>
                                <option value="Punjab">Punjab</option>
                                <option value="Rajasthan">Rajasthan</option>
                                <option value="Sikkim">Sikkim</option>
                                <option value="Tamil Nadu">Tamil Nadu</option>
                                <option value="Telangana">Telangana</option>
                                <option value="Tripura">Tripura</option>
                                <option value="Uttar Pradesh">Uttar Pradesh</option>
                                <option value="Uttarakhand">Uttarakhand</option>
                                <option value="West Bengal">West Bengal</option>
                            </select>
                        </div>

                        <!-- District -->
                        <div class="col-md-6 mb-3">
                            <label for="district" class="form-label">
                                <i class="fas fa-location-arrow me-2"></i>District
                            </label>
                            <select class="form-select" id="district" name="district" required>
                                <option value="">Select District</option>
                                <!-- Districts will be populated based on state selection -->
                            </select>
                        </div>

                        <!-- Soil Type -->
                        <div class="col-md-6 mb-3">
                            <label for="soil_type" class="form-label">
                                <i class="fas fa-mountain me-2"></i>Soil Type
                            </label>
                            <select class="form-select" id="soil_type" name="soil_type" required>
                                <option value="">Select Soil Type</option>
                                <option value="Black">Black Soil</option>
                                <option value="Clayey">Clayey Soil</option>
                                <option value="Loamy">Loamy Soil</option>
                                <option value="Red">Red Soil</option>
                                <option value="Sandy">Sandy Soil</option>
                            </select>
                        </div>

                        <!-- Nitrogen -->
                        <div class="col-md-6 mb-3">
                            <label for="nitrogen" class="form-label">
                                <i class="fas fa-atom me-2"></i>Nitrogen (N) - kg/ha
                            </label>
                            <input type="number" class="form-control" id="nitrogen" name="nitrogen" 
                                   min="0" max="200" step="0.1" required>
                            <div class="form-text text-light">Current nitrogen content</div>
                        </div>

                        <!-- Phosphorus -->
                        <div class="col-md-6 mb-3">
                            <label for="phosphorus" class="form-label">
                                <i class="fas fa-microscope me-2"></i>Phosphorus (P) - kg/ha
                            </label>
                            <input type="number" class="form-control" id="phosphorus" name="phosphorus" 
                                   min="0" max="150" step="0.1" required>
                            <div class="form-text text-light">Current phosphorus content</div>
                        </div>

                        <!-- Potassium -->
                        <div class="col-md-6 mb-3">
                            <label for="potassium" class="form-label">
                                <i class="fas fa-flask me-2"></i>Potassium (K) - kg/ha
                            </label>
                            <input type="number" class="form-control" id="potassium" name="potassium" 
                                   min="0" max="250" step="0.1" required>
                            <div class="form-text text-light">Current potassium content</div>
                        </div>

                        <!-- Temperature -->
                        <div class="col-md-6 mb-3">
                            <label for="temperature" class="form-label">
                                <i class="fas fa-thermometer-half me-2"></i>Temperature - °C
                            </label>
                            <input type="number" class="form-control" id="temperature" name="temperature" 
                                   min="0" max="50" step="0.1" required>
                            <div class="form-text text-light">Average temperature</div>
                        </div>

                        <!-- Humidity -->
                        <div class="col-md-6 mb-3">
                            <label for="humidity" class="form-label">
                                <i class="fas fa-tint me-2"></i>Humidity - %
                            </label>
                            <input type="number" class="form-control" id="humidity" name="humidity" 
                                   min="0" max="100" step="0.1" required>
                            <div class="form-text text-light">Relative humidity</div>
                        </div>

                        <!-- Moisture -->
                        <div class="col-md-6 mb-3">
                            <label for="moisture" class="form-label">
                                <i class="fas fa-water me-2"></i>Moisture - %
                            </label>
                            <input type="number" class="form-control" id="moisture" name="moisture" 
                                   min="0" max="100" step="0.1" required>
                            <div class="form-text text-light">Soil moisture content</div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-light btn-lg px-5">
                            <i class="fas fa-atom me-2"></i>Get Fertilizer Recommendation
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Results -->
        <div class="col-lg-6">
            <div id="loading" class="text-center" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Analyzing soil and crop requirements...</p>
            </div>

            <div id="results" style="display: none;">
                <!-- Recommended Fertilizer Card -->
                <div class="result-card card mb-4">
                    <div class="card-body text-center p-4">
                        <div class="fertilizer-icon">
                            <i class="fas fa-flask text-primary"></i>
                        </div>
                        <h3 class="fw-bold text-primary" id="recommendedFertilizer">Recommended Fertilizer</h3>
                        <p class="text-muted mb-3" id="fertilizerDescription">Loading...</p>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Confidence Level</label>
                            <div class="confidence-bar">
                                <div class="confidence-fill" id="confidenceFill" style="width: 0%"></div>
                            </div>
                            <div class="mt-2">
                                <span class="badge bg-success" id="confidenceText">0%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Nutrient Requirements -->
                <div class="result-card card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Nutrient Analysis
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="nutrientAnalysis">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Application Guidelines -->
                <div class="result-card card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-clipboard-list me-2"></i>Application Guidelines
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="applicationGuidelines">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Gemini AI Insights -->
                <div class="result-card card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-robot me-2"></i>AI Fertilizer Insights
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="geminiInsights">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Loading insights...</span>
                                </div>
                                <p class="mt-2 text-muted">Generating fertilizer recommendations...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sample Data Button -->
            <div class="text-center mb-3">
                <button type="button" class="btn btn-outline-primary" onclick="loadSampleData()">
                    <i class="fas fa-flask me-2"></i>Load Sample Data
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// District data for each state
const stateDistricts = {
    "Maharashtra": ["Mumbai", "Pune", "Nagpur", "Nashik", "Aurangabad", "Solapur", "Amravati", "Kolhapur", "Sangli", "Jalgaon"],
    "Karnataka": ["Bangalore", "Mysore", "Hubli", "Mangalore", "Belgaum", "Gulbarga", "Bellary", "Bijapur", "Shimoga", "Tumkur"],
    "Tamil Nadu": ["Chennai", "Coimbatore", "Madurai", "Salem", "Tiruchirappalli", "Tirunelveli", "Vellore", "Erode", "Tuticorin", "Dindigul"],
    "Andhra Pradesh": ["Hyderabad", "Visakhapatnam", "Vijayawada", "Guntur", "Nellore", "Kurnool", "Rajahmundry", "Tirupati", "Kadapa", "Anantapur"],
    "Kerala": ["Thiruvananthapuram", "Kochi", "Kozhikode", "Kollam", "Thrissur", "Palakkad", "Alappuzha", "Kannur", "Malappuram", "Kottayam"],
    "Gujarat": ["Ahmedabad", "Surat", "Vadodara", "Rajkot", "Bhavnagar", "Jamnagar", "Gandhinagar", "Anand", "Vapi", "Bharuch"],
    "West Bengal": ["Kolkata", "Howrah", "Durgapur", "Asansol", "Siliguri", "Bardhaman", "Malda", "Jalpaiguri", "Cooch Behar", "Purulia"],
    "Rajasthan": ["Jaipur", "Jodhpur", "Kota", "Bikaner", "Udaipur", "Ajmer", "Bhilwara", "Alwar", "Sikar", "Pali"],
    "Uttar Pradesh": ["Lucknow", "Kanpur", "Ghaziabad", "Agra", "Varanasi", "Meerut", "Allahabad", "Bareilly", "Aligarh", "Moradabad"],
    "Punjab": ["Ludhiana", "Amritsar", "Jalandhar", "Patiala", "Bathinda", "Mohali", "Firozpur", "Pathankot", "Moga", "Hoshiarpur"],
    "Haryana": ["Gurgaon", "Faridabad", "Panipat", "Ambala", "Yamunanagar", "Rohtak", "Hisar", "Karnal", "Sonipat", "Panchkula"],
    "Odisha": ["Bhubaneswar", "Cuttack", "Rourkela", "Berhampur", "Sambalpur", "Puri", "Balasore", "Bhadrak", "Baripada", "Jharsuguda"],
    "Madhya Pradesh": ["Bhopal", "Indore", "Gwalior", "Jabalpur", "Ujjain", "Sagar", "Dewas", "Satna", "Ratlam", "Rewa"],
    "Bihar": ["Patna", "Gaya", "Bhagalpur", "Muzaffarpur", "Darbhanga", "Bihar Sharif", "Arrah", "Begusarai", "Katihar", "Munger"],
    "Jharkhand": ["Ranchi", "Jamshedpur", "Dhanbad", "Bokaro", "Deoghar", "Phusro", "Hazaribagh", "Giridih", "Ramgarh", "Medininagar"],
    "Assam": ["Guwahati", "Silchar", "Dibrugarh", "Jorhat", "Nagaon", "Tinsukia", "Tezpur", "Bongaigaon", "Karimganj", "Sivasagar"],
    "Chhattisgarh": ["Raipur", "Bhilai", "Bilaspur", "Korba", "Durg", "Rajnandgaon", "Jagdalpur", "Raigarh", "Ambikapur", "Mahasamund"],
    "Uttarakhand": ["Dehradun", "Haridwar", "Roorkee", "Haldwani", "Rudrapur", "Kashipur", "Rishikesh", "Kotdwar", "Ramnagar", "Manglaur"],
    "Himachal Pradesh": ["Shimla", "Dharamshala", "Solan", "Mandi", "Palampur", "Baddi", "Nahan", "Paonta Sahib", "Sundarnagar", "Chamba"],
    "Goa": ["Panaji", "Vasco da Gama", "Margao", "Mapusa", "Ponda", "Bicholim", "Curchorem", "Sanquelim", "Valpoi", "Quepem"]
};

// Update districts when state changes
document.getElementById('state').addEventListener('change', function() {
    const state = this.value;
    const districtSelect = document.getElementById('district');
    
    // Clear existing options
    districtSelect.innerHTML = '<option value="">Select District</option>';
    
    if (state && stateDistricts[state]) {
        stateDistricts[state].forEach(district => {
            const option = document.createElement('option');
            option.value = district;
            option.textContent = district;
            districtSelect.appendChild(option);
        });
    }
});

document.getElementById('fertilizerRecommendationForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // Show loading
    document.getElementById('loading').style.display = 'block';
    document.getElementById('results').style.display = 'none';
    
    // Collect form data
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    try {
        // Make API call
        const response = await fetch('/api/fertilizer_recommendation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            displayResults(result);
        } else {
            showToast('Error: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('An error occurred while processing your request', 'error');
    } finally {
        document.getElementById('loading').style.display = 'none';
    }
});

function displayResults(result) {
    // Show results section
    document.getElementById('results').style.display = 'block';
    
    // Set main recommendation
    document.getElementById('recommendedFertilizer').textContent = result.recommendation;
    document.getElementById('fertilizerDescription').textContent = getFertilizerDescription(result.recommendation);
    
    // Set confidence
    const confidence = Math.round(result.confidence * 100);
    document.getElementById('confidenceText').textContent = confidence + '%';
    document.getElementById('confidenceFill').style.width = confidence + '%';
    
    // Display nutrient analysis
    if (result.nutrient_analysis) {
        displayNutrientAnalysis(result.nutrient_analysis);
    }
    
    // Display application guidelines
    displayApplicationGuidelines(result.recommendation);
    
    // Load Gemini insights
    loadGeminiInsights(result);
}

function displayNutrientAnalysis(nutrients) {
    const analysisHTML = `
        <div class="row">
            <div class="col-md-4">
                <div class="nutrient-card text-center">
                    <h5><i class="fas fa-atom me-2"></i>Nitrogen</h5>
                    <h3>${nutrients.nitrogen || 'N/A'} kg/ha</h3>
                    <small>Current Level</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="nutrient-card text-center">
                    <h5><i class="fas fa-microscope me-2"></i>Phosphorus</h5>
                    <h3>${nutrients.phosphorus || 'N/A'} kg/ha</h3>
                    <small>Current Level</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="nutrient-card text-center">
                    <h5><i class="fas fa-flask me-2"></i>Potassium</h5>
                    <h3>${nutrients.potassium || 'N/A'} kg/ha</h3>
                    <small>Current Level</small>
                </div>
            </div>
        </div>
    `;
    document.getElementById('nutrientAnalysis').innerHTML = analysisHTML;
}

function displayApplicationGuidelines(fertilizer) {
    const guidelines = getFertilizerGuidelines(fertilizer);
    const guidelinesHTML = `
        <div class="row">
            <div class="col-12">
                <h6><i class="fas fa-calendar me-2"></i>Application Timing:</h6>
                <p class="text-muted">${guidelines.timing}</p>
                
                <h6><i class="fas fa-weight me-2"></i>Dosage:</h6>
                <p class="text-muted">${guidelines.dosage}</p>
                
                <h6><i class="fas fa-hand-sparkles me-2"></i>Application Method:</h6>
                <p class="text-muted">${guidelines.method}</p>
                
                <h6><i class="fas fa-exclamation-triangle me-2"></i>Precautions:</h6>
                <p class="text-muted">${guidelines.precautions}</p>
            </div>
        </div>
    `;
    document.getElementById('applicationGuidelines').innerHTML = guidelinesHTML;
}

async function loadGeminiInsights(result) {
    try {
        const response = await fetch('/api/gemini_recommendation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                service: 'fertilizer_recommendation',
                recommendation: result.recommendation,
                confidence: result.confidence,
                input_data: result.input_data || {}
            })
        });
        
        const insights = await response.json();
        
        if (insights.success) {
            document.getElementById('geminiInsights').innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>Expert Recommendations:</h6>
                    <p class="mb-0">${insights.recommendation}</p>
                </div>
            `;
        } else {
            document.getElementById('geminiInsights').innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Unable to load AI insights at the moment.
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading Gemini insights:', error);
        document.getElementById('geminiInsights').innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Unable to load AI insights at the moment.
            </div>
        `;
    }
}

function getFertilizerDescription(fertilizer) {
    const descriptions = {
        'Urea': 'A nitrogen-rich fertilizer ideal for leafy growth and vegetative development.',
        'DAP': 'Diammonium Phosphate - provides both nitrogen and phosphorus for root development.',
        'NPK': 'A balanced fertilizer containing nitrogen, phosphorus, and potassium.',
        'MOP': 'Muriate of Potash - high in potassium for fruit and seed development.',
        'SSP': 'Single Super Phosphate - provides phosphorus and sulfur for plant growth.',
        'TSP': 'Triple Super Phosphate - concentrated phosphorus fertilizer.',
        'Compost': 'Organic fertilizer that improves soil structure and provides nutrients slowly.',
        'Vermicompost': 'Worm-processed organic matter rich in nutrients and beneficial microorganisms.'
    };
    return descriptions[fertilizer] || 'A suitable fertilizer for your crop and soil conditions.';
}

function getFertilizerGuidelines(fertilizer) {
    const guidelines = {
        'Urea': {
            timing: 'Apply during vegetative growth phase, split application recommended',
            dosage: '50-100 kg/ha depending on crop requirements',
            method: 'Broadcast and incorporate into soil, avoid surface application',
            precautions: 'Do not apply during flowering stage, ensure adequate moisture'
        },
        'DAP': {
            timing: 'Apply as basal dose before sowing or transplanting',
            dosage: '100-200 kg/ha based on soil test recommendations',
            method: 'Mix with soil at the time of land preparation',
            precautions: 'Store in dry place, avoid mixing with alkaline fertilizers'
        },
        'NPK': {
            timing: 'Apply as per crop growth stages - basal and top dressing',
            dosage: '150-300 kg/ha depending on NPK ratio and crop needs',
            method: 'Broadcast uniformly and incorporate into soil',
            precautions: 'Follow recommended NPK ratio for specific crops'
        },
        'MOP': {
            timing: 'Apply during fruit/pod filling stage for better quality',
            dosage: '50-150 kg/ha based on soil potassium levels',
            method: 'Side dressing or broadcasting with irrigation',
            precautions: 'Avoid over-application as it may affect calcium uptake'
        },
        'SSP': {
            timing: 'Apply as basal fertilizer before sowing',
            dosage: '200-400 kg/ha depending on phosphorus requirement',
            method: 'Mix thoroughly with soil during land preparation',
            precautions: 'Store in moisture-free environment to prevent caking'
        }
    };
    
    return guidelines[fertilizer] || {
        timing: 'Follow manufacturer recommendations and local agricultural practices',
        dosage: 'Based on soil test results and crop requirements',
        method: 'Apply uniformly and incorporate into soil',
        precautions: 'Store properly and follow safety guidelines'
    };
}

function loadSampleData() {
    document.getElementById('crop').value = 'Paddy';
    document.getElementById('state').value = 'Maharashtra';
    
    // Trigger state change to load districts
    document.getElementById('state').dispatchEvent(new Event('change'));
    
    setTimeout(() => {
        document.getElementById('district').value = 'Mumbai';
        document.getElementById('soil_type').value = 'Clayey';
        document.getElementById('nitrogen').value = '40';
        document.getElementById('phosphorus').value = '30';
        document.getElementById('potassium').value = '25';
        document.getElementById('temperature').value = '28';
        document.getElementById('humidity').value = '80';
        document.getElementById('moisture').value = '45';
    }, 100);
    
    showToast('Sample data loaded successfully!', 'success');
}
</script>
{% endblock %}