{% extends "base.html" %}

{% block title %}Crop Recommendation - Agricultural AI{% endblock %}

{% block extra_css %}
<style>
    .recommendation-form {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 2rem;
        color: white;
        margin-bottom: 2rem;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        border: none;
        padding: 12px 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.3);
        border: 2px solid #fff;
    }
    
    .result-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    
    .result-card:hover {
        transform: translateY(-5px);
    }
    
    .crop-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .confidence-bar {
        height: 10px;
        border-radius: 5px;
        background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #0abde3);
        position: relative;
        overflow: hidden;
    }
    
    .confidence-fill {
        height: 100%;
        background: linear-gradient(90deg, #ff9ff3, #54a0ff);
        border-radius: 5px;
        transition: width 0.8s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Header -->
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary">
            <i class="fas fa-seedling me-3"></i>Crop Recommendation System
        </h1>
        <p class="lead text-muted">
            Get AI-powered crop recommendations based on soil nutrients and climate conditions
        </p>
        <div class="d-flex justify-content-center gap-3 mb-4">
            <span class="badge bg-success fs-6">95% Accuracy</span>
            <span class="badge bg-info fs-6">22 Crops Supported</span>
            <span class="badge bg-warning fs-6">Ensemble ML</span>
        </div>
    </div>

    <div class="row">
        <!-- Input Form -->
        <div class="col-lg-6">
            <div class="recommendation-form">
                <h3 class="mb-4">
                    <i class="fas fa-leaf me-2"></i>Enter Soil & Climate Data
                </h3>
                
                <form id="cropRecommendationForm">
                    <div class="row">
                        <!-- Nitrogen -->
                        <div class="col-md-6 mb-3">
                            <label for="nitrogen" class="form-label">
                                <i class="fas fa-atom me-2"></i>Nitrogen (N) - kg/ha
                            </label>
                            <input type="number" class="form-control" id="nitrogen" name="nitrogen" 
                                   min="0" max="200" step="0.1" required>
                            <div class="form-text text-light">Typical range: 0-200 kg/ha</div>
                        </div>

                        <!-- Phosphorus -->
                        <div class="col-md-6 mb-3">
                            <label for="phosphorus" class="form-label">
                                <i class="fas fa-microscope me-2"></i>Phosphorus (P) - kg/ha
                            </label>
                            <input type="number" class="form-control" id="phosphorus" name="phosphorus" 
                                   min="0" max="150" step="0.1" required>
                            <div class="form-text text-light">Typical range: 0-150 kg/ha</div>
                        </div>

                        <!-- Potassium -->
                        <div class="col-md-6 mb-3">
                            <label for="potassium" class="form-label">
                                <i class="fas fa-flask me-2"></i>Potassium (K) - kg/ha
                            </label>
                            <input type="number" class="form-control" id="potassium" name="potassium" 
                                   min="0" max="250" step="0.1" required>
                            <div class="form-text text-light">Typical range: 0-250 kg/ha</div>
                        </div>

                        <!-- Temperature -->
                        <div class="col-md-6 mb-3">
                            <label for="temperature" class="form-label">
                                <i class="fas fa-thermometer-half me-2"></i>Temperature - °C
                            </label>
                            <input type="number" class="form-control" id="temperature" name="temperature" 
                                   min="0" max="50" step="0.1" required>
                            <div class="form-text text-light">Average temperature in °C</div>
                        </div>

                        <!-- Humidity -->
                        <div class="col-md-6 mb-3">
                            <label for="humidity" class="form-label">
                                <i class="fas fa-tint me-2"></i>Humidity - %
                            </label>
                            <input type="number" class="form-control" id="humidity" name="humidity" 
                                   min="0" max="100" step="0.1" required>
                            <div class="form-text text-light">Relative humidity percentage</div>
                        </div>

                        <!-- pH -->
                        <div class="col-md-6 mb-3">
                            <label for="ph" class="form-label">
                                <i class="fas fa-vial me-2"></i>pH Level
                            </label>
                            <input type="number" class="form-control" id="ph" name="ph" 
                                   min="0" max="14" step="0.1" required>
                            <div class="form-text text-light">Soil pH level (0-14)</div>
                        </div>

                        <!-- Rainfall -->
                        <div class="col-md-6 mb-3">
                            <label for="rainfall" class="form-label">
                                <i class="fas fa-cloud-rain me-2"></i>Rainfall - mm
                            </label>
                            <input type="number" class="form-control" id="rainfall" name="rainfall" 
                                   min="0" max="3000" step="0.1" required>
                            <div class="form-text text-light">Annual rainfall in mm</div>
                        </div>

                        <!-- State (Optional) -->
                        <div class="col-md-6 mb-3">
                            <label for="state" class="form-label">
                                <i class="fas fa-map-marker-alt me-2"></i>State (Optional)
                            </label>
                            <select class="form-select" id="state" name="state">
                                <option value="">Select State</option>
                                <option value="Andhra Pradesh">Andhra Pradesh</option>
                                <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                                <option value="Assam">Assam</option>
                                <option value="Bihar">Bihar</option>
                                <option value="Chhattisgarh">Chhattisgarh</option>
                                <option value="Goa">Goa</option>
                                <option value="Gujarat">Gujarat</option>
                                <option value="Haryana">Haryana</option>
                                <option value="Himachal Pradesh">Himachal Pradesh</option>
                                <option value="Jharkhand">Jharkhand</option>
                                <option value="Karnataka">Karnataka</option>
                                <option value="Kerala">Kerala</option>
                                <option value="Madhya Pradesh">Madhya Pradesh</option>
                                <option value="Maharashtra">Maharashtra</option>
                                <option value="Manipur">Manipur</option>
                                <option value="Meghalaya">Meghalaya</option>
                                <option value="Mizoram">Mizoram</option>
                                <option value="Nagaland">Nagaland</option>
                                <option value="Odisha">Odisha</option>
                                <option value="Punjab">Punjab</option>
                                <option value="Rajasthan">Rajasthan</option>
                                <option value="Sikkim">Sikkim</option>
                                <option value="Tamil Nadu">Tamil Nadu</option>
                                <option value="Telangana">Telangana</option>
                                <option value="Tripura">Tripura</option>
                                <option value="Uttar Pradesh">Uttar Pradesh</option>
                                <option value="Uttarakhand">Uttarakhand</option>
                                <option value="West Bengal">West Bengal</option>
                            </select>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-light btn-lg px-5">
                            <i class="fas fa-magic me-2"></i>Get Crop Recommendation
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Results -->
        <div class="col-lg-6">
            <div id="loading" class="text-center" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Analyzing soil conditions...</p>
            </div>

            <div id="results" style="display: none;">
                <!-- Recommended Crop Card -->
                <div class="result-card card mb-4">
                    <div class="card-body text-center p-4">
                        <div class="crop-icon" id="cropIcon">
                            <i class="fas fa-seedling text-success"></i>
                        </div>
                        <h3 class="fw-bold text-primary" id="recommendedCrop">Recommended Crop</h3>
                        <p class="text-muted mb-3" id="cropDescription">Loading...</p>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Confidence Level</label>
                            <div class="confidence-bar">
                                <div class="confidence-fill" id="confidenceFill" style="width: 0%"></div>
                            </div>
                            <div class="mt-2">
                                <span class="badge bg-success" id="confidenceText">0%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Alternative Crops -->
                <div class="result-card card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Alternative Recommendations
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="alternativeCrops">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Gemini AI Insights -->
                <div class="result-card card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-robot me-2"></i>AI Farming Insights
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="geminiInsights">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Loading insights...</span>
                                </div>
                                <p class="mt-2 text-muted">Generating personalized farming advice...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sample Data Button -->
            <div class="text-center mb-3">
                <button type="button" class="btn btn-outline-primary" onclick="loadSampleData()">
                    <i class="fas fa-flask me-2"></i>Load Sample Data
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('cropRecommendationForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // Show loading
    document.getElementById('loading').style.display = 'block';
    document.getElementById('results').style.display = 'none';
    
    // Collect form data
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    try {
        // Make API call
        const response = await fetch('/api/crop_recommendation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            displayResults(result);
        } else {
            showToast('Error: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('An error occurred while processing your request', 'error');
    } finally {
        document.getElementById('loading').style.display = 'none';
    }
});

function displayResults(result) {
    // Show results section
    document.getElementById('results').style.display = 'block';
    
    // Set main recommendation
    document.getElementById('recommendedCrop').textContent = result.recommendation;
    document.getElementById('cropDescription').textContent = getCropDescription(result.recommendation);
    document.getElementById('cropIcon').innerHTML = getCropIcon(result.recommendation);
    
    // Set confidence
    const confidence = Math.round(result.confidence * 100);
    document.getElementById('confidenceText').textContent = confidence + '%';
    document.getElementById('confidenceFill').style.width = confidence + '%';
    
    // Display alternatives if available
    if (result.alternatives && result.alternatives.length > 0) {
        const alternativesHTML = result.alternatives.map(alt => `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span><i class="fas fa-leaf me-2 text-success"></i>${alt.crop}</span>
                <span class="badge bg-secondary">${Math.round(alt.confidence * 100)}%</span>
            </div>
        `).join('');
        document.getElementById('alternativeCrops').innerHTML = alternativesHTML;
    }
    
    // Load Gemini insights
    loadGeminiInsights(result);
}

async function loadGeminiInsights(result) {
    try {
        const response = await fetch('/api/gemini_recommendation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                service: 'crop_recommendation',
                recommendation: result.recommendation,
                confidence: result.confidence,
                input_data: result.input_data || {}
            })
        });
        
        const insights = await response.json();
        
        if (insights.success) {
            document.getElementById('geminiInsights').innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>Expert Recommendations:</h6>
                    <p class="mb-0">${insights.recommendation}</p>
                </div>
            `;
        } else {
            document.getElementById('geminiInsights').innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Unable to load AI insights at the moment.
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading Gemini insights:', error);
        document.getElementById('geminiInsights').innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Unable to load AI insights at the moment.
            </div>
        `;
    }
}

function getCropIcon(crop) {
    const icons = {
        'rice': '<i class="fas fa-seedling text-success"></i>',
        'wheat': '<i class="fas fa-wheat text-warning"></i>',
        'maize': '<i class="fas fa-corn text-warning"></i>',
        'cotton': '<i class="fas fa-cotton-bureau text-info"></i>',
        'sugarcane': '<i class="fas fa-leaf text-success"></i>',
        'coconut': '<i class="fas fa-tree text-success"></i>',
        'banana': '<i class="fas fa-seedling text-warning"></i>',
        'grapes': '<i class="fas fa-grape-cluster text-purple"></i>',
        'apple': '<i class="fas fa-apple-alt text-danger"></i>',
        'orange': '<i class="fas fa-lemon text-warning"></i>',
        'pomegranate': '<i class="fas fa-seedling text-danger"></i>',
        'mango': '<i class="fas fa-leaf text-warning"></i>',
        'papaya': '<i class="fas fa-seedling text-orange"></i>',
        'watermelon': '<i class="fas fa-seedling text-success"></i>',
        'muskmelon': '<i class="fas fa-seedling text-warning"></i>',
        'chickpea': '<i class="fas fa-circle text-warning"></i>',
        'kidneybeans': '<i class="fas fa-seedling text-danger"></i>',
        'pigeonpeas': '<i class="fas fa-circle text-success"></i>',
        'mothbeans': '<i class="fas fa-seedling text-success"></i>',
        'blackgram': '<i class="fas fa-circle text-dark"></i>',
        'lentil': '<i class="fas fa-circle text-warning"></i>',
        'coffee': '<i class="fas fa-coffee text-brown"></i>'
    };
    return icons[crop.toLowerCase()] || '<i class="fas fa-seedling text-success"></i>';
}

function getCropDescription(crop) {
    const descriptions = {
        'rice': 'A staple grain crop that requires warm climate and abundant water supply.',
        'wheat': 'A cereal grain that thrives in temperate climates with moderate rainfall.',
        'maize': 'A versatile crop that adapts well to various climatic conditions.',
        'cotton': 'A fiber crop that requires warm climate and moderate rainfall.',
        'sugarcane': 'A cash crop that requires hot and humid climate with heavy rainfall.',
        'coconut': 'A tropical crop that requires coastal climate with high humidity.',
        'banana': 'A fruit crop that thrives in tropical and subtropical regions.',
        'grapes': 'A fruit crop that requires mediterranean climate with dry summers.',
        'apple': 'A temperate fruit crop that requires cold winters and mild summers.',
        'orange': 'A citrus fruit that thrives in subtropical and tropical regions.',
        'pomegranate': 'A fruit crop that adapts well to arid and semi-arid regions.',
        'mango': 'A tropical fruit crop that requires warm climate throughout the year.',
        'papaya': 'A tropical fruit that requires warm and humid climate.',
        'watermelon': 'A summer fruit crop that requires warm climate and adequate water.',
        'muskmelon': 'A summer fruit that thrives in warm and dry climate.',
        'chickpea': 'A legume crop that requires cool and dry climate.',
        'kidneybeans': 'A legume crop that thrives in temperate climate.',
        'pigeonpeas': 'A drought-tolerant legume crop suitable for dry regions.',
        'mothbeans': 'A drought-resistant legume crop ideal for arid regions.',
        'blackgram': 'A pulse crop that adapts well to various soil conditions.',
        'lentil': 'A cool-season legume crop that requires well-drained soil.',
        'coffee': 'A cash crop that requires tropical highland climate with adequate rainfall.'
    };
    return descriptions[crop.toLowerCase()] || 'A valuable crop suitable for your soil and climate conditions.';
}

function loadSampleData() {
    document.getElementById('nitrogen').value = '90';
    document.getElementById('phosphorus').value = '42';
    document.getElementById('potassium').value = '43';
    document.getElementById('temperature').value = '20.9';
    document.getElementById('humidity').value = '82.0';
    document.getElementById('ph').value = '6.5';
    document.getElementById('rainfall').value = '202.9';
    document.getElementById('state').value = 'Maharashtra';
    
    showToast('Sample data loaded successfully!', 'success');
}
</script>
{% endblock %}