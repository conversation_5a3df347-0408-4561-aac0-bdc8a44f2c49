{% extends "base.html" %}

{% block title %}Yield Prediction - Agricultural AI{% endblock %}

{% block extra_css %}
<style>
    .yield-form {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 2rem;
        color: white;
        margin-bottom: 2rem;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        border: none;
        padding: 12px 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.3);
        border: 2px solid #fff;
    }
    
    .result-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    
    .result-card:hover {
        transform: translateY(-5px);
    }
    
    .yield-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .yield-gauge {
        width: 200px;
        height: 200px;
        border-radius: 50%;
        background: conic-gradient(
            #ff6b6b 0deg 72deg,
            #feca57 72deg 144deg,
            #48dbfb 144deg 216deg,
            #0abde3 216deg 288deg,
            #ff9ff3 288deg 360deg
        );
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        position: relative;
    }
    
    .yield-gauge::before {
        content: '';
        width: 160px;
        height: 160px;
        background: white;
        border-radius: 50%;
        position: absolute;
    }
    
    .yield-value {
        position: relative;
        z-index: 1;
        font-size: 2rem;
        font-weight: bold;
        color: #333;
    }
    
    .performance-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        text-align: center;
    }
    
    .accuracy-bar {
        height: 10px;
        border-radius: 5px;
        background: #e9ecef;
        position: relative;
        overflow: hidden;
    }
    
    .accuracy-fill {
        height: 100%;
        background: linear-gradient(90deg, #28a745, #20c997);
        border-radius: 5px;
        transition: width 0.8s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Header -->
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary">
            <i class="fas fa-chart-line me-3"></i>Crop Yield Prediction System
        </h1>
        <p class="lead text-muted">
            Predict crop yields using advanced machine learning models and historical data
        </p>
        <div class="d-flex justify-content-center gap-3 mb-4">
            <span class="badge bg-success fs-6">R² > 0.85</span>
            <span class="badge bg-info fs-6">Multi-crop Support</span>
            <span class="badge bg-warning fs-6">Ensemble ML</span>
        </div>
    </div>

    <div class="row">
        <!-- Input Form -->
        <div class="col-lg-6">
            <div class="yield-form">
                <h3 class="mb-4">
                    <i class="fas fa-calculator me-2"></i>Enter Crop & Field Details
                </h3>
                
                <form id="yieldPredictionForm">
                    <div class="row">
                        <!-- Crop Type -->
                        <div class="col-md-12 mb-3">
                            <label for="crop" class="form-label">
                                <i class="fas fa-seedling me-2"></i>Crop Type
                            </label>
                            <select class="form-select" id="crop" name="crop" required>
                                <option value="">Select Crop</option>
                                <option value="Arecanut">Arecanut</option>
                                <option value="Arhar/Tur">Arhar/Tur</option>
                                <option value="Bajra">Bajra</option>
                                <option value="Banana">Banana</option>
                                <option value="Barley">Barley</option>
                                <option value="Black pepper">Black pepper</option>
                                <option value="Cardamom">Cardamom</option>
                                <option value="Cashewnut">Cashewnut</option>
                                <option value="Castor seed">Castor seed</option>
                                <option value="Coconut">Coconut</option>
                                <option value="Coriander">Coriander</option>
                                <option value="Cotton">Cotton</option>
                                <option value="Cowpea">Cowpea</option>
                                <option value="Dry chillies">Dry chillies</option>
                                <option value="Garlic">Garlic</option>
                                <option value="Ginger">Ginger</option>
                                <option value="Gram">Gram</option>
                                <option value="Groundnut">Groundnut</option>
                                <option value="Jowar">Jowar</option>
                                <option value="Jute">Jute</option>
                                <option value="Linseed">Linseed</option>
                                <option value="Maize">Maize</option>
                                <option value="Masoor">Masoor</option>
                                <option value="Moong">Moong</option>
                                <option value="Niger seed">Niger seed</option>
                                <option value="Onion">Onion</option>
                                <option value="Other Rabi pulses">Other Rabi pulses</option>
                                <option value="Potato">Potato</option>
                                <option value="Rapeseed &Mustard">Rapeseed &Mustard</option>
                                <option value="Rice">Rice</option>
                                <option value="Safflower">Safflower</option>
                                <option value="Sesamum">Sesamum</option>
                                <option value="Small millets">Small millets</option>
                                <option value="Sugarcane">Sugarcane</option>
                                <option value="Sunflower">Sunflower</option>
                                <option value="Sweet potato">Sweet potato</option>
                                <option value="Tapioca">Tapioca</option>
                                <option value="Tobacco">Tobacco</option>
                                <option value="Turmeric">Turmeric</option>
                                <option value="Urad">Urad</option>
                                <option value="Wheat">Wheat</option>
                            </select>
                        </div>

                        <!-- State -->
                        <div class="col-md-6 mb-3">
                            <label for="state" class="form-label">
                                <i class="fas fa-map-marker-alt me-2"></i>State
                            </label>
                            <select class="form-select" id="state" name="state" required>
                                <option value="">Select State</option>
                                <option value="Andhra Pradesh">Andhra Pradesh</option>
                                <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                                <option value="Assam">Assam</option>
                                <option value="Bihar">Bihar</option>
                                <option value="Chhattisgarh">Chhattisgarh</option>
                                <option value="Goa">Goa</option>
                                <option value="Gujarat">Gujarat</option>
                                <option value="Haryana">Haryana</option>
                                <option value="Himachal Pradesh">Himachal Pradesh</option>
                                <option value="Jharkhand">Jharkhand</option>
                                <option value="Karnataka">Karnataka</option>
                                <option value="Kerala">Kerala</option>
                                <option value="Madhya Pradesh">Madhya Pradesh</option>
                                <option value="Maharashtra">Maharashtra</option>
                                <option value="Manipur">Manipur</option>
                                <option value="Meghalaya">Meghalaya</option>
                                <option value="Mizoram">Mizoram</option>
                                <option value="Nagaland">Nagaland</option>
                                <option value="Odisha">Odisha</option>
                                <option value="Punjab">Punjab</option>
                                <option value="Rajasthan">Rajasthan</option>
                                <option value="Sikkim">Sikkim</option>
                                <option value="Tamil Nadu">Tamil Nadu</option>
                                <option value="Telangana">Telangana</option>
                                <option value="Tripura">Tripura</option>
                                <option value="Uttar Pradesh">Uttar Pradesh</option>
                                <option value="Uttarakhand">Uttarakhand</option>
                                <option value="West Bengal">West Bengal</option>
                            </select>
                        </div>

                        <!-- District -->
                        <div class="col-md-6 mb-3">
                            <label for="district" class="form-label">
                                <i class="fas fa-location-arrow me-2"></i>District
                            </label>
                            <select class="form-select" id="district" name="district" required>
                                <option value="">Select District</option>
                                <!-- Districts will be populated based on state selection -->
                            </select>
                        </div>

                        <!-- Season -->
                        <div class="col-md-6 mb-3">
                            <label for="season" class="form-label">
                                <i class="fas fa-calendar me-2"></i>Season
                            </label>
                            <select class="form-select" id="season" name="season" required>
                                <option value="">Select Season</option>
                                <option value="Kharif">Kharif (Monsoon)</option>
                                <option value="Rabi">Rabi (Winter)</option>
                                <option value="Summer">Summer</option>
                                <option value="Whole Year">Whole Year</option>
                                <option value="Autumn">Autumn</option>
                            </select>
                        </div>

                        <!-- Area -->
                        <div class="col-md-6 mb-3">
                            <label for="area" class="form-label">
                                <i class="fas fa-expand-arrows-alt me-2"></i>Area (Hectares)
                            </label>
                            <input type="number" class="form-control" id="area" name="area" 
                                   min="0.1" max="10000" step="0.1" required>
                            <div class="form-text text-light">Cultivated area in hectares</div>
                        </div>

                        <!-- Average Rainfall -->
                        <div class="col-md-6 mb-3">
                            <label for="rainfall" class="form-label">
                                <i class="fas fa-cloud-rain me-2"></i>Annual Rainfall (mm)
                            </label>
                            <input type="number" class="form-control" id="rainfall" name="rainfall" 
                                   min="0" max="5000" step="0.1" required>
                            <div class="form-text text-light">Average annual rainfall</div>
                        </div>

                        <!-- Average Temperature -->
                        <div class="col-md-6 mb-3">
                            <label for="temperature" class="form-label">
                                <i class="fas fa-thermometer-half me-2"></i>Average Temperature (°C)
                            </label>
                            <input type="number" class="form-control" id="temperature" name="temperature" 
                                   min="-10" max="50" step="0.1" required>
                            <div class="form-text text-light">Average temperature during growing season</div>
                        </div>

                        <!-- Pesticides Used -->
                        <div class="col-md-6 mb-3">
                            <label for="pesticides" class="form-label">
                                <i class="fas fa-spray-can me-2"></i>Pesticides Used (kg/ha)
                            </label>
                            <input type="number" class="form-control" id="pesticides" name="pesticides" 
                                   min="0" max="100" step="0.1" required>
                            <div class="form-text text-light">Total pesticides applied per hectare</div>
                        </div>

                        <!-- Year -->
                        <div class="col-md-6 mb-3">
                            <label for="year" class="form-label">
                                <i class="fas fa-calendar-alt me-2"></i>Year
                            </label>
                            <input type="number" class="form-control" id="year" name="year" 
                                   min="2000" max="2030" required>
                            <div class="form-text text-light">Cultivation year</div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-light btn-lg px-5">
                            <i class="fas fa-chart-line me-2"></i>Predict Yield
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Results -->
        <div class="col-lg-6">
            <div id="loading" class="text-center" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Analyzing crop and environmental data...</p>
            </div>

            <div id="results" style="display: none;">
                <!-- Yield Prediction Card -->
                <div class="result-card card mb-4">
                    <div class="card-body text-center p-4">
                        <div class="yield-icon">
                            <i class="fas fa-chart-line text-success"></i>
                        </div>
                        <h3 class="fw-bold text-primary mb-3">Predicted Yield</h3>
                        
                        <div class="yield-gauge mb-3">
                            <div class="yield-value" id="yieldValue">0</div>
                        </div>
                        
                        <h4 class="text-success" id="yieldText">0 tonnes/hectare</h4>
                        <p class="text-muted" id="yieldDescription">Loading...</p>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Model Accuracy (R²)</label>
                            <div class="accuracy-bar">
                                <div class="accuracy-fill" id="accuracyFill" style="width: 0%"></div>
                            </div>
                            <div class="mt-2">
                                <span class="badge bg-success" id="accuracyText">0%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <div class="result-card card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Performance Metrics
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="performance-card">
                                    <h6>Total Production</h6>
                                    <h4 id="totalProduction">0 tonnes</h4>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="performance-card">
                                    <h6>Productivity Index</h6>
                                    <h4 id="productivityIndex">0</h4>
                                </div>
                            </div>
                        </div>
                        
                        <div id="yieldBreakdown">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Improvement Suggestions -->
                <div class="result-card card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>Yield Improvement Tips
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="improvementSuggestions">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Gemini AI Insights -->
                <div class="result-card card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-robot me-2"></i>AI Yield Insights
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="geminiInsights">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Loading insights...</span>
                                </div>
                                <p class="mt-2 text-muted">Generating yield optimization advice...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sample Data Button -->
            <div class="text-center mb-3">
                <button type="button" class="btn btn-outline-primary" onclick="loadSampleData()">
                    <i class="fas fa-flask me-2"></i>Load Sample Data
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// District data for each state (same as fertilizer page)
const stateDistricts = {
    "Maharashtra": ["Mumbai", "Pune", "Nagpur", "Nashik", "Aurangabad", "Solapur", "Amravati", "Kolhapur", "Sangli", "Jalgaon"],
    "Karnataka": ["Bangalore", "Mysore", "Hubli", "Mangalore", "Belgaum", "Gulbarga", "Bellary", "Bijapur", "Shimoga", "Tumkur"],
    "Tamil Nadu": ["Chennai", "Coimbatore", "Madurai", "Salem", "Tiruchirappalli", "Tirunelveli", "Vellore", "Erode", "Tuticorin", "Dindigul"],
    "Andhra Pradesh": ["Hyderabad", "Visakhapatnam", "Vijayawada", "Guntur", "Nellore", "Kurnool", "Rajahmundry", "Tirupati", "Kadapa", "Anantapur"],
    "Kerala": ["Thiruvananthapuram", "Kochi", "Kozhikode", "Kollam", "Thrissur", "Palakkad", "Alappuzha", "Kannur", "Malappuram", "Kottayam"],
    "Gujarat": ["Ahmedabad", "Surat", "Vadodara", "Rajkot", "Bhavnagar", "Jamnagar", "Gandhinagar", "Anand", "Vapi", "Bharuch"],
    "West Bengal": ["Kolkata", "Howrah", "Durgapur", "Asansol", "Siliguri", "Bardhaman", "Malda", "Jalpaiguri", "Cooch Behar", "Purulia"],
    "Rajasthan": ["Jaipur", "Jodhpur", "Kota", "Bikaner", "Udaipur", "Ajmer", "Bhilwara", "Alwar", "Sikar", "Pali"],
    "Uttar Pradesh": ["Lucknow", "Kanpur", "Ghaziabad", "Agra", "Varanasi", "Meerut", "Allahabad", "Bareilly", "Aligarh", "Moradabad"],
    "Punjab": ["Ludhiana", "Amritsar", "Jalandhar", "Patiala", "Bathinda", "Mohali", "Firozpur", "Pathankot", "Moga", "Hoshiarpur"],
    "Haryana": ["Gurgaon", "Faridabad", "Panipat", "Ambala", "Yamunanagar", "Rohtak", "Hisar", "Karnal", "Sonipat", "Panchkula"],
    "Odisha": ["Bhubaneswar", "Cuttack", "Rourkela", "Berhampur", "Sambalpur", "Puri", "Balasore", "Bhadrak", "Baripada", "Jharsuguda"],
    "Madhya Pradesh": ["Bhopal", "Indore", "Gwalior", "Jabalpur", "Ujjain", "Sagar", "Dewas", "Satna", "Ratlam", "Rewa"],
    "Bihar": ["Patna", "Gaya", "Bhagalpur", "Muzaffarpur", "Darbhanga", "Bihar Sharif", "Arrah", "Begusarai", "Katihar", "Munger"],
    "Jharkhand": ["Ranchi", "Jamshedpur", "Dhanbad", "Bokaro", "Deoghar", "Phusro", "Hazaribagh", "Giridih", "Ramgarh", "Medininagar"],
    "Assam": ["Guwahati", "Silchar", "Dibrugarh", "Jorhat", "Nagaon", "Tinsukia", "Tezpur", "Bongaigaon", "Karimganj", "Sivasagar"],
    "Chhattisgarh": ["Raipur", "Bhilai", "Bilaspur", "Korba", "Durg", "Rajnandgaon", "Jagdalpur", "Raigarh", "Ambikapur", "Mahasamund"],
    "Uttarakhand": ["Dehradun", "Haridwar", "Roorkee", "Haldwani", "Rudrapur", "Kashipur", "Rishikesh", "Kotdwar", "Ramnagar", "Manglaur"],
    "Himachal Pradesh": ["Shimla", "Dharamshala", "Solan", "Mandi", "Palampur", "Baddi", "Nahan", "Paonta Sahib", "Sundarnagar", "Chamba"],
    "Goa": ["Panaji", "Vasco da Gama", "Margao", "Mapusa", "Ponda", "Bicholim", "Curchorem", "Sanquelim", "Valpoi", "Quepem"]
};

// Update districts when state changes
document.getElementById('state').addEventListener('change', function() {
    const state = this.value;
    const districtSelect = document.getElementById('district');
    
    // Clear existing options
    districtSelect.innerHTML = '<option value="">Select District</option>';
    
    if (state && stateDistricts[state]) {
        stateDistricts[state].forEach(district => {
            const option = document.createElement('option');
            option.value = district;
            option.textContent = district;
            districtSelect.appendChild(option);
        });
    }
});

document.getElementById('yieldPredictionForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // Show loading
    document.getElementById('loading').style.display = 'block';
    document.getElementById('results').style.display = 'none';
    
    // Collect form data
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    try {
        // Make API call
        const response = await fetch('/api/yield_prediction', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            displayResults(result);
        } else {
            showToast('Error: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('An error occurred while processing your request', 'error');
    } finally {
        document.getElementById('loading').style.display = 'none';
    }
});

function displayResults(result) {
    // Show results section
    document.getElementById('results').style.display = 'block';
    
    // Set yield prediction
    const yield_value = parseFloat(result.prediction).toFixed(2);
    document.getElementById('yieldValue').textContent = yield_value;
    document.getElementById('yieldText').textContent = `${yield_value} tonnes/hectare`;
    document.getElementById('yieldDescription').textContent = getYieldDescription(yield_value, result.crop);
    
    // Set model accuracy
    const accuracy = Math.round((result.accuracy || 0.85) * 100);
    document.getElementById('accuracyText').textContent = accuracy + '%';
    document.getElementById('accuracyFill').style.width = accuracy + '%';
    
    // Calculate total production
    const area = parseFloat(result.input_data?.area || 1);
    const totalProduction = (yield_value * area).toFixed(2);
    document.getElementById('totalProduction').textContent = `${totalProduction} tonnes`;
    
    // Calculate productivity index
    const productivityIndex = calculateProductivityIndex(yield_value, result.crop);
    document.getElementById('productivityIndex').textContent = productivityIndex;
    
    // Display improvement suggestions
    displayImprovementSuggestions(result);
    
    // Load Gemini insights
    loadGeminiInsights(result);
}

function getYieldDescription(yield_value, crop) {
    const yieldRanges = {
        'Rice': { low: 2, medium: 4, high: 6 },
        'Wheat': { low: 1.5, medium: 3, high: 5 },
        'Maize': { low: 2, medium: 5, high: 8 },
        'Cotton': { low: 0.3, medium: 0.6, high: 1 },
        'Sugarcane': { low: 40, medium: 70, high: 100 },
        'default': { low: 1, medium: 3, high: 5 }
    };
    
    const ranges = yieldRanges[crop] || yieldRanges['default'];
    
    if (yield_value < ranges.low) {
        return `Below average yield for ${crop}. Consider soil improvement and better farming practices.`;
    } else if (yield_value < ranges.medium) {
        return `Average yield for ${crop}. Good potential for improvement with optimization.`;
    } else if (yield_value < ranges.high) {
        return `Above average yield for ${crop}. Excellent farming practices being followed.`;
    } else {
        return `Outstanding yield for ${crop}. Exceptional farming conditions and practices.`;
    }
}

function calculateProductivityIndex(yield_value, crop) {
    // Simplified productivity index calculation
    const benchmarks = {
        'Rice': 4.0,
        'Wheat': 3.0,
        'Maize': 5.0,
        'Cotton': 0.6,
        'Sugarcane': 70.0,
        'default': 3.0
    };
    
    const benchmark = benchmarks[crop] || benchmarks['default'];
    const index = (yield_value / benchmark * 100).toFixed(0);
    return index;
}

function displayImprovementSuggestions(result) {
    const suggestions = getImprovementSuggestions(result);
    const suggestionsHTML = suggestions.map(suggestion => `
        <div class="alert alert-info mb-2">
            <i class="fas fa-lightbulb me-2"></i>
            <strong>${suggestion.title}:</strong> ${suggestion.description}
        </div>
    `).join('');
    
    document.getElementById('improvementSuggestions').innerHTML = suggestionsHTML;
}

function getImprovementSuggestions(result) {
    const suggestions = [
        {
            title: "Soil Health Management",
            description: "Regular soil testing and organic matter addition can improve nutrient availability and soil structure."
        },
        {
            title: "Water Management",
            description: "Implement efficient irrigation systems like drip irrigation to optimize water usage and reduce stress."
        },
        {
            title: "Integrated Pest Management",
            description: "Use biological controls and targeted pesticide application to minimize crop damage while reducing chemical inputs."
        },
        {
            title: "Crop Rotation",
            description: "Rotate with leguminous crops to naturally improve soil nitrogen content and break pest cycles."
        },
        {
            title: "Climate Adaptation",
            description: "Use climate-resilient varieties and adjust planting dates based on weather forecasts for optimal growing conditions."
        }
    ];
    
    return suggestions.slice(0, 3); // Return top 3 suggestions
}

async function loadGeminiInsights(result) {
    try {
        const response = await fetch('/api/gemini_recommendation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                service: 'yield_prediction',
                prediction: result.prediction,
                accuracy: result.accuracy,
                input_data: result.input_data || {}
            })
        });
        
        const insights = await response.json();
        
        if (insights.success) {
            document.getElementById('geminiInsights').innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>Expert Recommendations:</h6>
                    <p class="mb-0">${insights.recommendation}</p>
                </div>
            `;
        } else {
            document.getElementById('geminiInsights').innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Unable to load AI insights at the moment.
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading Gemini insights:', error);
        document.getElementById('geminiInsights').innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Unable to load AI insights at the moment.
            </div>
        `;
    }
}

function loadSampleData() {
    document.getElementById('crop').value = 'Rice';
    document.getElementById('state').value = 'Maharashtra';
    
    // Trigger state change to load districts
    document.getElementById('state').dispatchEvent(new Event('change'));
    
    setTimeout(() => {
        document.getElementById('district').value = 'Nagpur';
        document.getElementById('season').value = 'Kharif';
        document.getElementById('area').value = '2.5';
        document.getElementById('rainfall').value = '1200';
        document.getElementById('temperature').value = '26.5';
        document.getElementById('pesticides').value = '2.5';
        document.getElementById('year').value = '2024';
    }, 100);
    
    showToast('Sample data loaded successfully!', 'success');
}
</script>
{% endblock %}