{"module": "keras.src.models.functional", "class_name": "Functional", "config": {"name": "SimplePlantDiseaseModel", "trainable": true, "layers": [{"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_shape": [null, 128, 128, 3], "dtype": "float32", "sparse": false, "ragged": false, "name": "input_images"}, "registered_name": null, "name": "input_images", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "RandomFlip", "config": {"name": "random_flip_1", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "seed": null, "mode": "horizontal_and_vertical", "data_format": "channels_last"}, "registered_name": null, "build_config": {"input_shape": [null, 128, 128, 3]}, "name": "random_flip_1", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 128, 128, 3], "dtype": "float32", "keras_history": ["input_images", 0, 0]}}], "kwargs": {"training": true}}]}, {"module": "keras.layers", "class_name": "RandomRotation", "config": {"name": "random_rotation_1", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "factor": [-0.1, 0.1], "data_format": "channels_last", "fill_mode": "reflect", "fill_value": 0.0, "interpolation": "bilinear", "seed": null}, "registered_name": null, "build_config": {"input_shape": [null, 128, 128, 3]}, "name": "random_rotation_1", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 128, 128, 3], "dtype": "float32", "keras_history": ["random_flip_1", 0, 0]}}], "kwargs": {"training": true}}]}, {"module": "keras.layers", "class_name": "RandomZoom", "config": {"name": "random_zoom_1", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "height_factor": 0.1, "width_factor": null, "fill_mode": "reflect", "interpolation": "bilinear", "seed": null, "fill_value": 0.0, "data_format": "channels_last"}, "registered_name": null, "build_config": {"input_shape": [null, 128, 128, 3]}, "name": "random_zoom_1", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 128, 128, 3], "dtype": "float32", "keras_history": ["random_rotation_1", 0, 0]}}], "kwargs": {"training": true}}]}, {"module": "keras.src.models.functional", "class_name": "Functional", "config": {"name": "mobilenetv2_0.50_128", "trainable": true, "layers": [{"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_shape": [null, 128, 128, 3], "dtype": "float32", "sparse": false, "ragged": false, "name": "input_layer_3"}, "registered_name": null, "name": "input_layer_3", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "Conv1", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 16, "kernel_size": [3, 3], "strides": [2, 2], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 128, 128, 3]}, "name": "Conv1", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 128, 128, 3], "dtype": "float32", "keras_history": ["input_layer_3", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "bn_Conv1", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 64, 64, 16]}, "name": "bn_Conv1", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64, 64, 16], "dtype": "float32", "keras_history": ["Conv1", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "Conv1_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "Conv1_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64, 64, 16], "dtype": "float32", "keras_history": ["bn_Conv1", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "expanded_conv_depthwise", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 64, 64, 16]}, "name": "expanded_conv_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64, 64, 16], "dtype": "float32", "keras_history": ["Conv1_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "expanded_conv_depthwise_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 64, 64, 16]}, "name": "expanded_conv_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64, 64, 16], "dtype": "float32", "keras_history": ["expanded_conv_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "expanded_conv_depthwise_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "expanded_conv_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64, 64, 16], "dtype": "float32", "keras_history": ["expanded_conv_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "expanded_conv_project", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 8, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 64, 64, 16]}, "name": "expanded_conv_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64, 64, 16], "dtype": "float32", "keras_history": ["expanded_conv_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "expanded_conv_project_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 64, 64, 8]}, "name": "expanded_conv_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64, 64, 8], "dtype": "float32", "keras_history": ["expanded_conv_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_1_expand", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 48, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 64, 64, 8]}, "name": "block_1_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64, 64, 8], "dtype": "float32", "keras_history": ["expanded_conv_project_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_1_expand_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 64, 64, 48]}, "name": "block_1_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64, 64, 48], "dtype": "float32", "keras_history": ["block_1_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_1_expand_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_1_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64, 64, 48], "dtype": "float32", "keras_history": ["block_1_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "ZeroPadding2D", "config": {"name": "block_1_pad", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "padding": [[0, 1], [0, 1]], "data_format": "channels_last"}, "registered_name": null, "build_config": {"input_shape": [null, 64, 64, 48]}, "name": "block_1_pad", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64, 64, 48], "dtype": "float32", "keras_history": ["block_1_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_1_depthwise", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [2, 2], "padding": "valid", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 65, 65, 48]}, "name": "block_1_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 65, 65, 48], "dtype": "float32", "keras_history": ["block_1_pad", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_1_depthwise_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 32, 32, 48]}, "name": "block_1_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 48], "dtype": "float32", "keras_history": ["block_1_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_1_depthwise_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_1_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 48], "dtype": "float32", "keras_history": ["block_1_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_1_project", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 16, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 32, 32, 48]}, "name": "block_1_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 48], "dtype": "float32", "keras_history": ["block_1_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_1_project_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 32, 32, 16]}, "name": "block_1_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 16], "dtype": "float32", "keras_history": ["block_1_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_2_expand", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 96, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 32, 32, 16]}, "name": "block_2_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 16], "dtype": "float32", "keras_history": ["block_1_project_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_2_expand_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 32, 32, 96]}, "name": "block_2_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 96], "dtype": "float32", "keras_history": ["block_2_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_2_expand_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_2_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 96], "dtype": "float32", "keras_history": ["block_2_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_2_depthwise", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 32, 32, 96]}, "name": "block_2_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 96], "dtype": "float32", "keras_history": ["block_2_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_2_depthwise_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 32, 32, 96]}, "name": "block_2_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 96], "dtype": "float32", "keras_history": ["block_2_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_2_depthwise_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_2_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 96], "dtype": "float32", "keras_history": ["block_2_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_2_project", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 16, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 32, 32, 96]}, "name": "block_2_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 96], "dtype": "float32", "keras_history": ["block_2_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_2_project_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 32, 32, 16]}, "name": "block_2_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 16], "dtype": "float32", "keras_history": ["block_2_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "block_2_add", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 32, 32, 16], [null, 32, 32, 16]]}, "name": "block_2_add", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 16], "dtype": "float32", "keras_history": ["block_1_project_BN", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 16], "dtype": "float32", "keras_history": ["block_2_project_BN", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_3_expand", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 96, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 32, 32, 16]}, "name": "block_3_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 16], "dtype": "float32", "keras_history": ["block_2_add", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_3_expand_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 32, 32, 96]}, "name": "block_3_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 96], "dtype": "float32", "keras_history": ["block_3_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_3_expand_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_3_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 96], "dtype": "float32", "keras_history": ["block_3_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "ZeroPadding2D", "config": {"name": "block_3_pad", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "padding": [[0, 1], [0, 1]], "data_format": "channels_last"}, "registered_name": null, "build_config": {"input_shape": [null, 32, 32, 96]}, "name": "block_3_pad", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 32, 32, 96], "dtype": "float32", "keras_history": ["block_3_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_3_depthwise", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [2, 2], "padding": "valid", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 33, 33, 96]}, "name": "block_3_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 33, 33, 96], "dtype": "float32", "keras_history": ["block_3_pad", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_3_depthwise_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 96]}, "name": "block_3_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_3_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_3_depthwise_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_3_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_3_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_3_project", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 16, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 96]}, "name": "block_3_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_3_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_3_project_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 16]}, "name": "block_3_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 16], "dtype": "float32", "keras_history": ["block_3_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_4_expand", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 96, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 16]}, "name": "block_4_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 16], "dtype": "float32", "keras_history": ["block_3_project_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_4_expand_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 96]}, "name": "block_4_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_4_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_4_expand_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_4_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_4_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_4_depthwise", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 96]}, "name": "block_4_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_4_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_4_depthwise_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 96]}, "name": "block_4_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_4_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_4_depthwise_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_4_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_4_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_4_project", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 16, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 96]}, "name": "block_4_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_4_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_4_project_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 16]}, "name": "block_4_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 16], "dtype": "float32", "keras_history": ["block_4_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "block_4_add", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 16, 16, 16], [null, 16, 16, 16]]}, "name": "block_4_add", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 16], "dtype": "float32", "keras_history": ["block_3_project_BN", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 16], "dtype": "float32", "keras_history": ["block_4_project_BN", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_5_expand", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 96, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 16]}, "name": "block_5_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 16], "dtype": "float32", "keras_history": ["block_4_add", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_5_expand_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 96]}, "name": "block_5_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_5_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_5_expand_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_5_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_5_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_5_depthwise", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 96]}, "name": "block_5_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_5_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_5_depthwise_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 96]}, "name": "block_5_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_5_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_5_depthwise_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_5_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_5_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_5_project", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 16, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 96]}, "name": "block_5_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_5_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_5_project_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 16]}, "name": "block_5_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 16], "dtype": "float32", "keras_history": ["block_5_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "block_5_add", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 16, 16, 16], [null, 16, 16, 16]]}, "name": "block_5_add", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 16], "dtype": "float32", "keras_history": ["block_4_add", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 16], "dtype": "float32", "keras_history": ["block_5_project_BN", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_6_expand", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 96, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 16]}, "name": "block_6_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 16], "dtype": "float32", "keras_history": ["block_5_add", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_6_expand_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 96]}, "name": "block_6_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_6_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_6_expand_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_6_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_6_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "ZeroPadding2D", "config": {"name": "block_6_pad", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "padding": [[0, 1], [0, 1]], "data_format": "channels_last"}, "registered_name": null, "build_config": {"input_shape": [null, 16, 16, 96]}, "name": "block_6_pad", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 16, 16, 96], "dtype": "float32", "keras_history": ["block_6_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_6_depthwise", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [2, 2], "padding": "valid", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 17, 17, 96]}, "name": "block_6_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 17, 17, 96], "dtype": "float32", "keras_history": ["block_6_pad", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_6_depthwise_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 96]}, "name": "block_6_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 96], "dtype": "float32", "keras_history": ["block_6_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_6_depthwise_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_6_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 96], "dtype": "float32", "keras_history": ["block_6_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_6_project", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 32, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 96]}, "name": "block_6_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 96], "dtype": "float32", "keras_history": ["block_6_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_6_project_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 32]}, "name": "block_6_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 32], "dtype": "float32", "keras_history": ["block_6_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_7_expand", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 192, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 32]}, "name": "block_7_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 32], "dtype": "float32", "keras_history": ["block_6_project_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_7_expand_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_7_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_7_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_7_expand_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_7_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_7_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_7_depthwise", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_7_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_7_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_7_depthwise_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_7_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_7_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_7_depthwise_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_7_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_7_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_7_project", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 32, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_7_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_7_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_7_project_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 32]}, "name": "block_7_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 32], "dtype": "float32", "keras_history": ["block_7_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "block_7_add", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 8, 8, 32], [null, 8, 8, 32]]}, "name": "block_7_add", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 32], "dtype": "float32", "keras_history": ["block_6_project_BN", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 32], "dtype": "float32", "keras_history": ["block_7_project_BN", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_8_expand", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 192, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 32]}, "name": "block_8_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 32], "dtype": "float32", "keras_history": ["block_7_add", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_8_expand_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_8_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_8_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_8_expand_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_8_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_8_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_8_depthwise", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_8_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_8_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_8_depthwise_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_8_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_8_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_8_depthwise_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_8_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_8_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_8_project", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 32, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_8_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_8_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_8_project_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 32]}, "name": "block_8_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 32], "dtype": "float32", "keras_history": ["block_8_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "block_8_add", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 8, 8, 32], [null, 8, 8, 32]]}, "name": "block_8_add", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 32], "dtype": "float32", "keras_history": ["block_7_add", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 32], "dtype": "float32", "keras_history": ["block_8_project_BN", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_9_expand", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 192, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 32]}, "name": "block_9_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 32], "dtype": "float32", "keras_history": ["block_8_add", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_9_expand_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_9_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_9_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_9_expand_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_9_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_9_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_9_depthwise", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_9_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_9_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_9_depthwise_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_9_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_9_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_9_depthwise_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_9_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_9_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_9_project", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 32, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_9_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_9_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_9_project_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 32]}, "name": "block_9_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 32], "dtype": "float32", "keras_history": ["block_9_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "block_9_add", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 8, 8, 32], [null, 8, 8, 32]]}, "name": "block_9_add", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 32], "dtype": "float32", "keras_history": ["block_8_add", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 32], "dtype": "float32", "keras_history": ["block_9_project_BN", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_10_expand", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 192, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 32]}, "name": "block_10_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 32], "dtype": "float32", "keras_history": ["block_9_add", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_10_expand_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_10_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_10_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_10_expand_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_10_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_10_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_10_depthwise", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_10_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_10_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_10_depthwise_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_10_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_10_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_10_depthwise_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_10_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_10_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_10_project", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 48, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 192]}, "name": "block_10_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 192], "dtype": "float32", "keras_history": ["block_10_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_10_project_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 48]}, "name": "block_10_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 48], "dtype": "float32", "keras_history": ["block_10_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_11_expand", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 288, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 48]}, "name": "block_11_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 48], "dtype": "float32", "keras_history": ["block_10_project_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_11_expand_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 288]}, "name": "block_11_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_11_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_11_expand_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_11_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_11_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_11_depthwise", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 288]}, "name": "block_11_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_11_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_11_depthwise_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 288]}, "name": "block_11_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_11_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_11_depthwise_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_11_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_11_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_11_project", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 48, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 288]}, "name": "block_11_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_11_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_11_project_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 48]}, "name": "block_11_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 48], "dtype": "float32", "keras_history": ["block_11_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "block_11_add", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 8, 8, 48], [null, 8, 8, 48]]}, "name": "block_11_add", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 48], "dtype": "float32", "keras_history": ["block_10_project_BN", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 48], "dtype": "float32", "keras_history": ["block_11_project_BN", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_12_expand", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 288, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 48]}, "name": "block_12_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 48], "dtype": "float32", "keras_history": ["block_11_add", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_12_expand_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 288]}, "name": "block_12_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_12_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_12_expand_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_12_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_12_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_12_depthwise", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 288]}, "name": "block_12_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_12_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_12_depthwise_BN", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 288]}, "name": "block_12_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_12_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_12_depthwise_relu", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_12_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_12_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_12_project", "trainable": false, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 48, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 288]}, "name": "block_12_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_12_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_12_project_BN", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 48]}, "name": "block_12_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 48], "dtype": "float32", "keras_history": ["block_12_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "block_12_add", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 8, 8, 48], [null, 8, 8, 48]]}, "name": "block_12_add", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 48], "dtype": "float32", "keras_history": ["block_11_add", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 48], "dtype": "float32", "keras_history": ["block_12_project_BN", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_13_expand", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 288, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 48]}, "name": "block_13_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 48], "dtype": "float32", "keras_history": ["block_12_add", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_13_expand_BN", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 288]}, "name": "block_13_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_13_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_13_expand_relu", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_13_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_13_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "ZeroPadding2D", "config": {"name": "block_13_pad", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "padding": [[0, 1], [0, 1]], "data_format": "channels_last"}, "registered_name": null, "build_config": {"input_shape": [null, 8, 8, 288]}, "name": "block_13_pad", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 8, 8, 288], "dtype": "float32", "keras_history": ["block_13_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_13_depthwise", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [2, 2], "padding": "valid", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 9, 9, 288]}, "name": "block_13_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 9, 9, 288], "dtype": "float32", "keras_history": ["block_13_pad", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_13_depthwise_BN", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 288]}, "name": "block_13_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 288], "dtype": "float32", "keras_history": ["block_13_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_13_depthwise_relu", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_13_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 288], "dtype": "float32", "keras_history": ["block_13_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_13_project", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 80, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 288]}, "name": "block_13_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 288], "dtype": "float32", "keras_history": ["block_13_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_13_project_BN", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 80]}, "name": "block_13_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 80], "dtype": "float32", "keras_history": ["block_13_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_14_expand", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 480, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 80]}, "name": "block_14_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 80], "dtype": "float32", "keras_history": ["block_13_project_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_14_expand_BN", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 480]}, "name": "block_14_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_14_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_14_expand_relu", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_14_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_14_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_14_depthwise", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 480]}, "name": "block_14_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_14_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_14_depthwise_BN", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 480]}, "name": "block_14_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_14_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_14_depthwise_relu", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_14_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_14_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_14_project", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 80, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 480]}, "name": "block_14_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_14_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_14_project_BN", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 80]}, "name": "block_14_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 80], "dtype": "float32", "keras_history": ["block_14_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "block_14_add", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 4, 4, 80], [null, 4, 4, 80]]}, "name": "block_14_add", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 80], "dtype": "float32", "keras_history": ["block_13_project_BN", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 80], "dtype": "float32", "keras_history": ["block_14_project_BN", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_15_expand", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 480, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 80]}, "name": "block_15_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 80], "dtype": "float32", "keras_history": ["block_14_add", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_15_expand_BN", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 480]}, "name": "block_15_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_15_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_15_expand_relu", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_15_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_15_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_15_depthwise", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 480]}, "name": "block_15_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_15_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_15_depthwise_BN", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 480]}, "name": "block_15_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_15_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_15_depthwise_relu", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_15_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_15_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_15_project", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 80, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 480]}, "name": "block_15_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_15_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_15_project_BN", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 80]}, "name": "block_15_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 80], "dtype": "float32", "keras_history": ["block_15_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "block_15_add", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 4, 4, 80], [null, 4, 4, 80]]}, "name": "block_15_add", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 80], "dtype": "float32", "keras_history": ["block_14_add", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 80], "dtype": "float32", "keras_history": ["block_15_project_BN", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_16_expand", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 480, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 80]}, "name": "block_16_expand", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 80], "dtype": "float32", "keras_history": ["block_15_add", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_16_expand_BN", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 480]}, "name": "block_16_expand_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_16_expand", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_16_expand_relu", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_16_expand_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_16_expand_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "DepthwiseConv2D", "config": {"name": "block_16_depthwise", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "depth_multiplier": 1, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": false, "depthwise_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "depthwise_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "depthwise_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 480]}, "name": "block_16_depthwise", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_16_expand_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_16_depthwise_BN", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 480]}, "name": "block_16_depthwise_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_16_depthwise", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "block_16_depthwise_relu", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "block_16_depthwise_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_16_depthwise_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "block_16_project", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 160, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 480]}, "name": "block_16_project", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 480], "dtype": "float32", "keras_history": ["block_16_depthwise_relu", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "block_16_project_BN", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 160]}, "name": "block_16_project_BN", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 160], "dtype": "float32", "keras_history": ["block_16_project", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "Conv_1", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 1280, "kernel_size": [1, 1], "strides": [1, 1], "padding": "valid", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "linear", "use_bias": false, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 160]}, "name": "Conv_1", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 160], "dtype": "float32", "keras_history": ["block_16_project_BN", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "Conv_1_bn", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.999, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 4, 4, 1280]}, "name": "Conv_1_bn", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 1280], "dtype": "float32", "keras_history": ["Conv_1", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "ReLU", "config": {"name": "out_relu", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "registered_name": null, "name": "out_relu", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 1280], "dtype": "float32", "keras_history": ["Conv_1_bn", 0, 0]}}], "kwargs": {}}]}], "input_layers": ["input_layer_3", 0, 0], "output_layers": ["out_relu", 0, 0]}, "registered_name": "Functional", "build_config": {"input_shape": null}, "compile_config": {}, "name": "mobilenetv2_0.50_128", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 128, 128, 3], "dtype": "float32", "keras_history": ["random_zoom_1", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "GlobalAveragePooling2D", "config": {"name": "global_average_pooling2d_2", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "data_format": "channels_last", "keepdims": false}, "registered_name": null, "name": "global_average_pooling2d_2", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 1280], "dtype": "float32", "keras_history": ["mobilenetv2_0.50_128", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Reshape", "config": {"name": "reshape_2", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "target_shape": [1, 1, 1280]}, "registered_name": null, "build_config": {"input_shape": [null, 1280]}, "name": "reshape_2", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 1280], "dtype": "float32", "keras_history": ["global_average_pooling2d_2", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "conv2d_6", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 160, "kernel_size": [1, 1], "strides": [1, 1], "padding": "valid", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "relu", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 1, 1, 1280]}, "name": "conv2d_6", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 1, 1, 1280], "dtype": "float32", "keras_history": ["reshape_2", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv2D", "config": {"name": "conv2d_7", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 1280, "kernel_size": [1, 1], "strides": [1, 1], "padding": "valid", "data_format": "channels_last", "dilation_rate": [1, 1], "groups": 1, "activation": "sigmoid", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 1, 1, 160]}, "name": "conv2d_7", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 1, 1, 160], "dtype": "float32", "keras_history": ["conv2d_6", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Multiply", "config": {"name": "multiply_1", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 4, 4, 1280], [null, 1, 1, 1280]]}, "name": "multiply_1", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 1280], "dtype": "float32", "keras_history": ["mobilenetv2_0.50_128", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 1, 1, 1280], "dtype": "float32", "keras_history": ["conv2d_7", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "GlobalAveragePooling2D", "config": {"name": "global_average_pooling2d_3", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "data_format": "channels_last", "keepdims": false}, "registered_name": null, "name": "global_average_pooling2d_3", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 4, 4, 1280], "dtype": "float32", "keras_history": ["multiply_1", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_2", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "rate": 0.3, "seed": null, "noise_shape": null}, "registered_name": null, "name": "dropout_2", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 1280], "dtype": "float32", "keras_history": ["global_average_pooling2d_3", 0, 0]}}], "kwargs": {"training": false}}]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_6", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "units": 64, "activation": "relu", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 1280]}, "name": "dense_6", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 1280], "dtype": "float32", "keras_history": ["dropout_2", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_3", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "rate": 0.2, "seed": null, "noise_shape": null}, "registered_name": null, "name": "dropout_3", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64], "dtype": "float32", "keras_history": ["dense_6", 0, 0]}}], "kwargs": {"training": false}}]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_7", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "units": 38, "activation": "softmax", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 64]}, "name": "dense_7", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64], "dtype": "float32", "keras_history": ["dropout_3", 0, 0]}}], "kwargs": {}}]}], "input_layers": ["input_images", 0, 0], "output_layers": ["dense_7", 0, 0]}, "registered_name": "Functional", "build_config": {"input_shape": null}, "compile_config": {"optimizer": {"module": "keras.optimizers", "class_name": "<PERSON>", "config": {"name": "adam", "learning_rate": 9.999999747378752e-05, "weight_decay": null, "clipnorm": null, "global_clipnorm": null, "clipvalue": null, "use_ema": false, "ema_momentum": 0.99, "ema_overwrite_frequency": null, "loss_scale_factor": null, "gradient_accumulation_steps": null, "beta_1": 0.9, "beta_2": 0.999, "epsilon": 1e-07, "amsgrad": false}, "registered_name": null}, "loss": "categorical_crossentropy", "loss_weights": null, "metrics": ["accuracy"], "weighted_metrics": null, "run_eagerly": false, "steps_per_execution": 1, "jit_compile": false}}