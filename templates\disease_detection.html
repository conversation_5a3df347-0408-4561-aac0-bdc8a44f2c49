{% extends "base.html" %}

{% block title %}Plant Disease Detection - Agricultural AI{% endblock %}

{% block extra_css %}
<style>
    .detection-form {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 2rem;
        color: white;
        margin-bottom: 2rem;
    }
    
    .upload-area {
        border: 3px dashed rgba(255, 255, 255, 0.5);
        border-radius: 15px;
        padding: 3rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.1);
    }
    
    .upload-area:hover {
        border-color: rgba(255, 255, 255, 0.8);
        background: rgba(255, 255, 255, 0.2);
    }
    
    .upload-area.dragover {
        border-color: #fff;
        background: rgba(255, 255, 255, 0.3);
    }
    
    .upload-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.8;
    }
    
    .result-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    
    .result-card:hover {
        transform: translateY(-5px);
    }
    
    .disease-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .confidence-circle {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background: conic-gradient(
            #28a745 0deg,
            #28a745 calc(var(--percentage) * 3.6deg),
            #e9ecef calc(var(--percentage) * 3.6deg),
            #e9ecef 360deg
        );
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        position: relative;
    }
    
    .confidence-circle::before {
        content: '';
        width: 120px;
        height: 120px;
        background: white;
        border-radius: 50%;
        position: absolute;
    }
    
    .confidence-value {
        position: relative;
        z-index: 1;
        font-size: 1.5rem;
        font-weight: bold;
        color: #333;
    }
    
    .image-preview {
        max-width: 100%;
        max-height: 300px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .treatment-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .severity-badge {
        font-size: 1rem;
        padding: 0.5rem 1rem;
        border-radius: 25px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Header -->
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary">
            <i class="fas fa-bug me-3"></i>Plant Disease Detection System
        </h1>
        <p class="lead text-muted">
            Upload plant images for instant disease detection using advanced computer vision
        </p>
        <div class="d-flex justify-content-center gap-3 mb-4">
            <span class="badge bg-success fs-6">98% Accuracy</span>
            <span class="badge bg-info fs-6">38 Disease Classes</span>
            <span class="badge bg-warning fs-6">EfficientNet + ViT</span>
        </div>
    </div>

    <div class="row">
        <!-- Upload Form -->
        <div class="col-lg-6">
            <div class="detection-form">
                <h3 class="mb-4">
                    <i class="fas fa-camera me-2"></i>Upload Plant Image
                </h3>
                
                <form id="diseaseDetectionForm" enctype="multipart/form-data">
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h4>Drop your image here</h4>
                        <p class="mb-3">or click to browse</p>
                        <input type="file" id="imageInput" name="image" accept="image/*" style="display: none;" required>
                        <button type="button" class="btn btn-light" onclick="document.getElementById('imageInput').click()">
                            <i class="fas fa-folder-open me-2"></i>Choose Image
                        </button>
                        <div class="form-text text-light mt-3">
                            Supported formats: JPG, PNG, JPEG (Max size: 10MB)
                        </div>
                    </div>
                    
                    <!-- Image Preview -->
                    <div id="imagePreview" class="text-center mt-4" style="display: none;">
                        <img id="previewImg" class="image-preview" alt="Preview">
                        <div class="mt-3">
                            <button type="button" class="btn btn-outline-light me-2" onclick="removeImage()">
                                <i class="fas fa-trash me-2"></i>Remove
                            </button>
                            <button type="submit" class="btn btn-light">
                                <i class="fas fa-search me-2"></i>Detect Disease
                            </button>
                        </div>
                    </div>
                </form>
                
                <!-- Sample Images -->
                <div class="mt-4">
                    <h5><i class="fas fa-images me-2"></i>Try Sample Images</h5>
                    <div class="row">
                        <div class="col-4">
                            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%2364b5f6'/%3E%3Ctext x='50' y='50' text-anchor='middle' dy='.3em' fill='white' font-size='12'%3EHealthy%3C/text%3E%3C/svg%3E" 
                                 class="img-fluid rounded cursor-pointer" 
                                 onclick="loadSampleImage('healthy')" 
                                 title="Healthy Leaf">
                        </div>
                        <div class="col-4">
                            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23ff7043'/%3E%3Ctext x='50' y='50' text-anchor='middle' dy='.3em' fill='white' font-size='12'%3EBlight%3C/text%3E%3C/svg%3E" 
                                 class="img-fluid rounded cursor-pointer" 
                                 onclick="loadSampleImage('blight')" 
                                 title="Blight Disease">
                        </div>
                        <div class="col-4">
                            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23ab47bc'/%3E%3Ctext x='50' y='50' text-anchor='middle' dy='.3em' fill='white' font-size='12'%3ESpot%3C/text%3E%3C/svg%3E" 
                                 class="img-fluid rounded cursor-pointer" 
                                 onclick="loadSampleImage('spot')" 
                                 title="Leaf Spot">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results -->
        <div class="col-lg-6">
            <div id="loading" class="text-center" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Analyzing plant image...</p>
                <div class="progress mt-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 100%"></div>
                </div>
            </div>

            <div id="results" style="display: none;">
                <!-- Detection Results Card -->
                <div class="result-card card mb-4">
                    <div class="card-body text-center p-4">
                        <div class="disease-icon" id="diseaseIcon">
                            <i class="fas fa-leaf text-success"></i>
                        </div>
                        <h3 class="fw-bold" id="detectedDisease">Disease Detection</h3>
                        <p class="text-muted mb-3" id="diseaseDescription">Loading...</p>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Confidence Level</label>
                            <div class="confidence-circle" id="confidenceCircle" style="--percentage: 0">
                                <div class="confidence-value" id="confidenceValue">0%</div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <span class="severity-badge" id="severityBadge">Unknown</span>
                        </div>
                    </div>
                </div>

                <!-- Alternative Predictions -->
                <div class="result-card card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Alternative Predictions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="alternativePredictions">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Treatment Recommendations -->
                <div class="result-card card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-prescription-bottle me-2"></i>Treatment Recommendations
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="treatmentRecommendations">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Prevention Tips -->
                <div class="result-card card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>Prevention & Management
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="preventionTips">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Gemini AI Insights -->
                <div class="result-card card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-robot me-2"></i>AI Disease Insights
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="geminiInsights">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Loading insights...</span>
                                </div>
                                <p class="mt-2 text-muted">Generating expert disease management advice...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upload Instructions -->
            <div class="card border-0">
                <div class="card-body">
                    <h5><i class="fas fa-info-circle me-2 text-primary"></i>Upload Tips</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Use clear, well-lit images</li>
                        <li><i class="fas fa-check text-success me-2"></i>Focus on affected leaf areas</li>
                        <li><i class="fas fa-check text-success me-2"></i>Avoid blurry or distant shots</li>
                        <li><i class="fas fa-check text-success me-2"></i>Include healthy parts for comparison</li>
                        <li><i class="fas fa-check text-success me-2"></i>Multiple angles can improve accuracy</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Upload area drag and drop functionality
const uploadArea = document.getElementById('uploadArea');
const imageInput = document.getElementById('imageInput');

uploadArea.addEventListener('click', () => imageInput.click());

uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        imageInput.files = files;
        previewImage(files[0]);
    }
});

imageInput.addEventListener('change', (e) => {
    if (e.target.files.length > 0) {
        previewImage(e.target.files[0]);
    }
});

function previewImage(file) {
    if (file.size > 10 * 1024 * 1024) {
        showToast('Image size should be less than 10MB', 'error');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = (e) => {
        document.getElementById('previewImg').src = e.target.result;
        document.getElementById('imagePreview').style.display = 'block';
    };
    reader.readAsDataURL(file);
}

function removeImage() {
    document.getElementById('imageInput').value = '';
    document.getElementById('imagePreview').style.display = 'none';
    document.getElementById('results').style.display = 'none';
}

document.getElementById('diseaseDetectionForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const imageFile = formData.get('image');
    
    if (!imageFile || imageFile.size === 0) {
        showToast('Please select an image first', 'error');
        return;
    }
    
    // Show loading
    document.getElementById('loading').style.display = 'block';
    document.getElementById('results').style.display = 'none';
    
    try {
        // Make API call
        const response = await fetch('/api/disease_detection', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            displayResults(result);
        } else {
            showToast('Error: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('An error occurred while processing your request', 'error');
    } finally {
        document.getElementById('loading').style.display = 'none';
    }
});

function displayResults(result) {
    // Show results section
    document.getElementById('results').style.display = 'block';
    
    // Set main detection result
    const disease = result.prediction;
    const confidence = Math.round(result.confidence * 100);
    
    document.getElementById('detectedDisease').textContent = disease;
    document.getElementById('diseaseDescription').textContent = getDiseaseDescription(disease);
    document.getElementById('diseaseIcon').innerHTML = getDiseaseIcon(disease);
    document.getElementById('confidenceValue').textContent = confidence + '%';
    document.getElementById('confidenceCircle').style.setProperty('--percentage', confidence);
    
    // Set severity badge
    const severity = getDiseaseSeverity(disease);
    const severityBadge = document.getElementById('severityBadge');
    severityBadge.textContent = severity.text;
    severityBadge.className = `severity-badge ${severity.class}`;
    
    // Display alternatives if available
    if (result.alternatives && result.alternatives.length > 0) {
        displayAlternatives(result.alternatives);
    }
    
    // Display treatment recommendations
    displayTreatmentRecommendations(disease);
    
    // Display prevention tips
    displayPreventionTips(disease);
    
    // Load Gemini insights
    loadGeminiInsights(result);
}

function displayAlternatives(alternatives) {
    const alternativesHTML = alternatives.map(alt => `
        <div class="d-flex justify-content-between align-items-center mb-2">
            <span>
                ${getDiseaseIcon(alt.class, 'fas fa-leaf me-2 text-muted')}
                ${alt.class}
            </span>
            <span class="badge bg-secondary">${Math.round(alt.confidence * 100)}%</span>
        </div>
    `).join('');
    document.getElementById('alternativePredictions').innerHTML = alternativesHTML || '<p class="text-muted">No alternatives detected</p>';
}

function displayTreatmentRecommendations(disease) {
    const treatments = getTreatmentRecommendations(disease);
    const treatmentHTML = treatments.map(treatment => `
        <div class="treatment-card">
            <h6><i class="fas fa-pills me-2"></i>${treatment.type}</h6>
            <p class="mb-0">${treatment.description}</p>
        </div>
    `).join('');
    document.getElementById('treatmentRecommendations').innerHTML = treatmentHTML;
}

function displayPreventionTips(disease) {
    const tips = getPreventionTips(disease);
    const tipsHTML = tips.map(tip => `
        <div class="alert alert-info mb-2">
            <i class="fas fa-lightbulb me-2"></i>
            <strong>${tip.title}:</strong> ${tip.description}
        </div>
    `).join('');
    document.getElementById('preventionTips').innerHTML = tipsHTML;
}

async function loadGeminiInsights(result) {
    try {
        const response = await fetch('/api/gemini_recommendation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                service: 'disease_detection',
                prediction: result.prediction,
                confidence: result.confidence,
                input_data: { disease: result.prediction }
            })
        });
        
        const insights = await response.json();
        
        if (insights.success) {
            document.getElementById('geminiInsights').innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>Expert Recommendations:</h6>
                    <p class="mb-0">${insights.recommendation}</p>
                </div>
            `;
        } else {
            document.getElementById('geminiInsights').innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Unable to load AI insights at the moment.
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading Gemini insights:', error);
        document.getElementById('geminiInsights').innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Unable to load AI insights at the moment.
            </div>
        `;
    }
}

function getDiseaseIcon(disease, defaultClass = '') {
    const icons = {
        'Healthy': '<i class="fas fa-leaf text-success"></i>',
        'Bacterial Blight': '<i class="fas fa-virus text-danger"></i>',
        'Brown Spot': '<i class="fas fa-circle text-warning"></i>',
        'Leaf Blast': '<i class="fas fa-fire text-danger"></i>',
        'Bacterial Leaf Streak': '<i class="fas fa-minus text-warning"></i>',
        'Leaf Scald': '<i class="fas fa-thermometer-three-quarters text-danger"></i>'
    };
    
    if (defaultClass) {
        return `<i class="${defaultClass}"></i>`;
    }
    
    return icons[disease] || '<i class="fas fa-bug text-warning"></i>';
}

function getDiseaseDescription(disease) {
    const descriptions = {
        'Healthy': 'The plant appears to be healthy with no visible signs of disease.',
        'Bacterial Blight': 'A bacterial infection causing water-soaked lesions on leaves.',
        'Brown Spot': 'Fungal disease causing brown circular spots on leaves.',
        'Leaf Blast': 'Destructive fungal disease affecting leaves and stems.',
        'Bacterial Leaf Streak': 'Bacterial infection causing long streaks on leaves.',
        'Leaf Scald': 'Physiological disorder often caused by environmental stress.'
    };
    return descriptions[disease] || 'Disease detected in the plant tissue.';
}

function getDiseaseSeverity(disease) {
    const severities = {
        'Healthy': { text: 'Healthy', class: 'bg-success' },
        'Bacterial Blight': { text: 'High', class: 'bg-danger' },
        'Brown Spot': { text: 'Medium', class: 'bg-warning' },
        'Leaf Blast': { text: 'High', class: 'bg-danger' },
        'Bacterial Leaf Streak': { text: 'Medium', class: 'bg-warning' },
        'Leaf Scald': { text: 'Low', class: 'bg-info' }
    };
    return severities[disease] || { text: 'Unknown', class: 'bg-secondary' };
}

function getTreatmentRecommendations(disease) {
    const treatments = {
        'Bacterial Blight': [
            { type: 'Chemical Control', description: 'Apply copper-based bactericides like Copper Oxychloride' },
            { type: 'Cultural Practice', description: 'Remove and destroy infected plant material' }
        ],
        'Brown Spot': [
            { type: 'Fungicide', description: 'Apply Mancozeb or Propiconazole fungicides' },
            { type: 'Field Sanitation', description: 'Ensure proper drainage and field hygiene' }
        ],
        'Leaf Blast': [
            { type: 'Systemic Fungicide', description: 'Use Tricyclazole or Isoprothiolane' },
            { type: 'Resistant Varieties', description: 'Plant blast-resistant crop varieties' }
        ],
        'Healthy': [
            { type: 'Preventive Care', description: 'Continue current healthy plant management practices' }
        ]
    };
    
    return treatments[disease] || [
        { type: 'Consultation', description: 'Consult with local agricultural extension officer' },
        { type: 'General Care', description: 'Maintain proper plant hygiene and nutrition' }
    ];
}

function getPreventionTips(disease) {
    return [
        {
            title: "Crop Rotation",
            description: "Rotate crops to break disease cycles and reduce pathogen buildup in soil."
        },
        {
            title: "Water Management",
            description: "Avoid overhead irrigation and ensure proper drainage to reduce humidity."
        },
        {
            title: "Plant Nutrition",
            description: "Maintain balanced nutrition to strengthen plant immune system."
        },
        {
            title: "Field Hygiene",
            description: "Remove crop debris and weeds that can harbor pathogens."
        }
    ];
}

function loadSampleImage(type) {
    // This would typically load actual sample images
    // For demo purposes, we'll simulate the process
    showToast(`Sample ${type} image loaded! Click "Detect Disease" to analyze.`, 'info');
    
    // Create a sample blob and set it to the input
    const canvas = document.createElement('canvas');
    canvas.width = 300;
    canvas.height = 300;
    const ctx = canvas.getContext('2d');
    
    // Draw a simple colored rectangle as sample
    const colors = { healthy: '#4CAF50', blight: '#F44336', spot: '#FF9800' };
    ctx.fillStyle = colors[type] || '#2196F3';
    ctx.fillRect(0, 0, 300, 300);
    
    // Add text
    ctx.fillStyle = 'white';
    ctx.font = '20px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`Sample ${type}`, 150, 150);
    
    canvas.toBlob((blob) => {
        const file = new File([blob], `sample_${type}.png`, { type: 'image/png' });
        const dt = new DataTransfer();
        dt.items.add(file);
        imageInput.files = dt.files;
        previewImage(file);
    });
}
</script>
{% endblock %}