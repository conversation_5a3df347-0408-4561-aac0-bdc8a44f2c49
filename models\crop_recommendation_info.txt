
CROP RECOMMENDATION MODEL INFO
==============================

Model Performance:
- Accuracy: 0.9932 (99.32%)
- Model Type: LGBMClassifier
- Features: 23 (including engineered features)
- Crops Supported: 22

Dataset Info:
- Training samples: 1760
- Test samples: 440
- Original features: 7 (N, P, K, temperature, humidity, ph, rainfall)
- Engineered features: 16

Top 5 Features:
  1. humidity: 0.1308
  2. rainfall: 0.1101
  3. K: 0.0960
  4. P: 0.0758
  5. temp_rainfall_ratio: 0.0756

Supported Crops:
apple, banana, blackgram, chickpea, coconut, coffee, cotton, grapes, jute, kidneybeans, lentil, maize, mango, mothbeans, mungbean, muskmelon, orange, papaya, pigeonpeas, pomegranate, rice, watermelon

Usage:
Load the model using joblib.load('models/crop_recommendation_model.pkl')
Use the predict_crop() function for predictions.
